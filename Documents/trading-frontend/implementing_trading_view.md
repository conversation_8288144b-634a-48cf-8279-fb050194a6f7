# TradingView Integration Guide

## Overview

This guide explains how to integrate TradingView charts with your Next.js frontend to display real-time financial data from your Alpha Vantage-powered Django backend.

## Backend Setup (✅ Completed)

Your Django backend now includes:

- ✅ **Alpha Vantage API Integration**: Real market data for stocks, forex, crypto, commodities, and indices
- ✅ **38 Pre-configured Assets**: Including AAPL, GOOGL, BTC/USDT, EUR/USD, GOLD, etc.
- ✅ **Real-time Price Updates**: Celery tasks fetch live data every minute
- ✅ **API Endpoints**: RESTful endpoints for accessing price data
- ✅ **Database Storage**: Historical price data with OHLCV (Open, High, Low, Close, Volume)

### Available API Endpoints

```
GET /api/alpha-vantage/live_quote/?symbol=AAPL
GET /api/alpha-vantage/price_history/?symbol=AAPL&days=7
GET /api/alpha-vantage/supported_assets/
POST /api/alpha-vantage/refresh_prices/
```

## Frontend Integration Options

### Option 1: TradingView Widgets (Recommended for Quick Setup)

**Pros**: Zero development effort, professional charts, real-time data
**Cons**: Uses TradingView's data, not your Alpha Vantage data

#### Installation

```bash
cd your-nextjs-app
npm install react-tradingview-widget
```

#### Basic Implementation

```jsx
// components/TradingChart.jsx
import { TradingViewEmbed } from 'react-tradingview-widget';

export default function TradingChart({ symbol = "NASDAQ:AAPL" }) {
  return (
    <div className="w-full h-96">
      <TradingViewEmbed
        widgetType="chart"
        width="100%"
        height="100%"
        symbol={symbol}
        theme="dark" // or "light"
        locale="en"
        toolbar_bg="#f1f3f6"
        enable_publishing={false}
        allow_symbol_change={true}
        details={true}
        hotlist={true}
        calendar={true}
        studies={[
          "Volume@tv-basicstudies",
          "MACD@tv-basicstudies"
        ]}
      />
    </div>
  );
}
```

#### Advanced Widget with Multiple Charts

```jsx
// components/TradingDashboard.jsx
import { TradingViewEmbed } from 'react-tradingview-widget';

export default function TradingDashboard() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      {/* Main Chart */}
      <div className="lg:col-span-2 h-96">
        <TradingViewEmbed
          widgetType="chart"
          width="100%"
          height="100%"
          symbol="NASDAQ:AAPL"
          theme="dark"
          style="1" // Candle
          locale="en"
          toolbar_bg="#f1f3f6"
          enable_publishing={false}
          allow_symbol_change={true}
          details={true}
          hotlist={true}
          calendar={true}
        />
      </div>
      
      {/* Market Overview */}
      <div className="h-64">
        <TradingViewEmbed
          widgetType="market-overview"
          width="100%"
          height="100%"
          colorTheme="dark"
          dateRange="12M"
          showChart={true}
          locale="en"
          largeChartUrl=""
          isTransparent={false}
          showSymbolLogo={true}
          showFloatingTooltip={false}
          plotLineColorGrowing="rgba(41, 98, 255, 1)"
          plotLineColorFalling="rgba(41, 98, 255, 1)"
          gridLineColor="rgba(240, 243, 250, 0)"
          scaleFontColor="rgba(120, 123, 134, 1)"
          belowLineFillColorGrowing="rgba(41, 98, 255, 0.12)"
          belowLineFillColorFalling="rgba(41, 98, 255, 0.12)"
          belowLineFillColorGrowingBottom="rgba(41, 98, 255, 0)"
          belowLineFillColorFallingBottom="rgba(41, 98, 255, 0)"
          symbolActiveColor="rgba(41, 98, 255, 0.12)"
        />
      </div>
      
      {/* Watchlist */}
      <div className="h-64">
        <TradingViewEmbed
          widgetType="watchlist"
          width="100%"
          height="100%"
          colorTheme="dark"
          dateRange="12M"
          showChart={true}
          locale="en"
          largeChartUrl=""
          isTransparent={false}
          showSymbolLogo={true}
          showFloatingTooltip={false}
          watchlist={[
            "NASDAQ:AAPL",
            "NASDAQ:GOOGL",
            "NASDAQ:MSFT",
            "NASDAQ:AMZN",
            "NASDAQ:TSLA"
          ]}
        />
      </div>
    </div>
  );
}
```

### Option 2: TradingView Charting Library (Advanced)

**Pros**: Use your own Alpha Vantage data, full customization
**Cons**: More complex setup, requires TradingView library

#### Setup Steps

1. **Get TradingView Charting Library**
   - use the free version with limitations

2. **Install Dependencies**
```bash
npm install lightweight-charts
```

3. **Create Custom Datafeed**

```jsx
// utils/tradingViewDatafeed.js
export class CustomDatafeed {
  constructor(apiUrl) {
    this.apiUrl = apiUrl;
  }

  onReady(callback) {
    setTimeout(() => {
      callback({
        supported_resolutions: ['1', '5', '15', '30', '60', '1D', '1W', '1M'],
        supports_group_request: false,
        supports_marks: false,
        supports_search: true,
        supports_timescale_marks: false,
      });
    }, 0);
  }

  searchSymbols(userInput, exchange, symbolType, onResultReadyCallback) {
    // Implement symbol search using your API
    fetch(`${this.apiUrl}/alpha-vantage/supported_assets/`)
      .then(response => response.json())
      .then(data => {
        const symbols = [];
        Object.entries(data.current_assets).forEach(([type, assets]) => {
          assets.forEach(asset => {
            symbols.push({
              symbol: asset.symbol,
              full_name: asset.name,
              description: asset.name,
              exchange: type,
              ticker: asset.symbol,
              type: type.toLowerCase()
            });
          });
        });
        onResultReadyCallback(symbols.filter(s => 
          s.symbol.toLowerCase().includes(userInput.toLowerCase())
        ));
      });
  }

  resolveSymbol(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
    // Resolve symbol information
    const symbolInfo = {
      name: symbolName,
      ticker: symbolName,
      description: symbolName,
      type: 'stock',
      session: '24x7',
      timezone: 'Etc/UTC',
      exchange: 'YOUR_EXCHANGE',
      minmov: 1,
      pricescale: 100,
      has_intraday: true,
      intraday_multipliers: ['1', '5', '15', '30', '60'],
      supported_resolutions: ['1', '5', '15', '30', '60', '1D', '1W', '1M'],
      volume_precision: 2,
      data_status: 'streaming',
    };
    
    setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
  }

  getBars(symbolInfo, resolution, from, to, onHistoryCallback, onErrorCallback, firstDataRequest) {
    // Fetch historical data from your API
    const days = Math.ceil((to - from) / (24 * 60 * 60));
    
    fetch(`${this.apiUrl}/alpha-vantage/price_history/?symbol=${symbolInfo.ticker}&days=${days}`)
      .then(response => response.json())
      .then(data => {
        const bars = data.price_history.map(item => ({
          time: new Date(item.timestamp).getTime(),
          low: item.low || item.price,
          high: item.high || item.price,
          open: item.open || item.price,
          close: item.price,
          volume: item.volume || 0
        }));
        
        onHistoryCallback(bars, { noData: bars.length === 0 });
      })
      .catch(onErrorCallback);
  }

  subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
    // Implement real-time data subscription
    // You can use WebSockets or polling here
    this.subscribeRealtime(symbolInfo.ticker, onRealtimeCallback);
  }

  unsubscribeBars(subscriberUID) {
    // Unsubscribe from real-time data
  }

  subscribeRealtime(symbol, callback) {
    // Poll for real-time updates every 30 seconds
    const interval = setInterval(() => {
      fetch(`${this.apiUrl}/alpha-vantage/live_quote/?symbol=${symbol}`)
        .then(response => response.json())
        .then(data => {
          if (data.live_data) {
            callback({
              time: new Date().getTime(),
              close: parseFloat(data.live_data.price),
              volume: data.live_data.volume || 0
            });
          }
        })
        .catch(console.error);
    }, 30000);
    
    return interval;
  }
}
```

4. **Create Chart Component**

```jsx
// components/CustomTradingChart.jsx
import { useEffect, useRef } from 'react';
import { CustomDatafeed } from '../utils/tradingViewDatafeed';

export default function CustomTradingChart({ symbol, theme = 'dark' }) {
  const chartContainerRef = useRef();
  const widgetRef = useRef();

  useEffect(() => {
    if (typeof window !== 'undefined' && window.TradingView) {
      const widget = new window.TradingView.widget({
        width: '100%',
        height: 400,
        symbol: symbol,
        interval: '5',
        container_id: chartContainerRef.current.id,
        datafeed: new CustomDatafeed(process.env.NEXT_PUBLIC_API_URL),
        library_path: '/charting_library/',
        locale: 'en',
        disabled_features: ['use_localstorage_for_settings'],
        enabled_features: ['study_templates'],
        charts_storage_url: 'https://saveload.tradingview.com',
        charts_storage_api_version: '1.1',
        client_id: 'tradingview.com',
        user_id: 'public_user_id',
        fullscreen: false,
        autosize: true,
        theme: theme,
      });

      widgetRef.current = widget;
    }

    return () => {
      if (widgetRef.current) {
        widgetRef.current.remove();
      }
    };
  }, [symbol, theme]);

  return (
    <div 
      ref={chartContainerRef} 
      id={`tradingview_${Math.random().toString(36).substr(2, 9)}`}
      className="w-full h-96"
    />
  );
}
```

### Option 3: Lightweight Charts (Simple & Fast)

**Pros**: Lightweight, fast, customizable, free
**Cons**: Less features than full TradingView

```jsx
// components/LightweightChart.jsx
import { useEffect, useRef, useState } from 'react';
import { createChart } from 'lightweight-charts';

export default function LightweightChart({ symbol, theme = 'dark' }) {
  const chartContainerRef = useRef();
  const chartRef = useRef();
  const [data, setData] = useState([]);

  useEffect(() => {
    // Fetch data from your API
    fetch(`${process.env.NEXT_PUBLIC_API_URL}/alpha-vantage/price_history/?symbol=${symbol}&days=30`)
      .then(response => response.json())
      .then(apiData => {
        const chartData = apiData.price_history.map(item => ({
          time: new Date(item.timestamp).getTime() / 1000,
          open: parseFloat(item.open || item.price),
          high: parseFloat(item.high || item.price),
          low: parseFloat(item.low || item.price),
          close: parseFloat(item.price)
        }));
        setData(chartData);
      });
  }, [symbol]);

  useEffect(() => {
    if (data.length > 0 && chartContainerRef.current) {
      const chart = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height: 400,
        layout: {
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#ffffff',
          textColor: theme === 'dark' ? '#d1d4dc' : '#191919',
        },
        grid: {
          vertLines: {
            color: theme === 'dark' ? '#2B2B43' : '#e1e1e1',
          },
          horzLines: {
            color: theme === 'dark' ? '#2B2B43' : '#e1e1e1',
          },
        },
        crosshair: {
          mode: 0,
        },
        rightPriceScale: {
          borderColor: theme === 'dark' ? '#485c7b' : '#cccccc',
        },
        timeScale: {
          borderColor: theme === 'dark' ? '#485c7b' : '#cccccc',
        },
      });

      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#4bffb5',
        downColor: '#ff4976',
        borderDownColor: '#ff4976',
        borderUpColor: '#4bffb5',
        wickDownColor: '#838ca1',
        wickUpColor: '#838ca1',
      });

      candlestickSeries.setData(data);
      chartRef.current = chart;

      // Handle resize
      const handleResize = () => {
        chart.applyOptions({ width: chartContainerRef.current.clientWidth });
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        chart.remove();
      };
    }
  }, [data, theme]);

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold">{symbol} Chart</h3>
      </div>
      <div ref={chartContainerRef} className="w-full h-96" />
    </div>
  );
}
```

## Integration in Your Trading Interface

### Complete Trading Page Example

```jsx
// pages/trading.jsx
import { useState, useEffect } from 'react';
import TradingChart from '../components/TradingChart';
import LightweightChart from '../components/LightweightChart';

export default function TradingPage() {
  const [selectedAsset, setSelectedAsset] = useState('AAPL');
  const [assets, setAssets] = useState([]);
  const [livePrice, setLivePrice] = useState(null);
  const [theme, setTheme] = useState('dark');

  useEffect(() => {
    // Fetch available assets
    fetch(`${process.env.NEXT_PUBLIC_API_URL}/alpha-vantage/supported_assets/`)
      .then(response => response.json())
      .then(data => {
        const allAssets = [];
        Object.entries(data.current_assets).forEach(([type, assetList]) => {
          allAssets.push(...assetList.map(asset => ({ ...asset, type })));
        });
        setAssets(allAssets);
      });
  }, []);

  useEffect(() => {
    // Fetch live price for selected asset
    const fetchLivePrice = () => {
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/alpha-vantage/live_quote/?symbol=${selectedAsset}`)
        .then(response => response.json())
        .then(data => {
          if (data.live_data) {
            setLivePrice(data.live_data);
          }
        })
        .catch(console.error);
    };

    fetchLivePrice();
    const interval = setInterval(fetchLivePrice, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [selectedAsset]);

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Trading Platform</h1>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              className="px-4 py-2 rounded bg-blue-600 hover:bg-blue-700"
            >
              {theme === 'dark' ? '☀️' : '🌙'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Asset Selector */}
          <div className="lg:col-span-1">
            <div className={`p-4 rounded-lg ${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <h3 className="text-lg font-semibold mb-4">Assets</h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {assets.map((asset) => (
                  <button
                    key={asset.symbol}
                    onClick={() => setSelectedAsset(asset.symbol)}
                    className={`w-full text-left p-2 rounded ${
                      selectedAsset === asset.symbol
                        ? 'bg-blue-600 text-white'
                        : theme === 'dark'
                        ? 'bg-gray-700 hover:bg-gray-600'
                        : 'bg-white hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium">{asset.symbol}</div>
                    <div className="text-sm opacity-75">{asset.name}</div>
                    <div className="text-xs opacity-50">{asset.type}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Chart and Trading Panel */}
          <div className="lg:col-span-3">
            {/* Live Price Display */}
            {livePrice && (
              <div className={`p-4 rounded-lg mb-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'}`}>
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold">{selectedAsset}</h2>
                  <div className="text-right">
                    <div className="text-2xl font-bold">
                      ${parseFloat(livePrice.price || livePrice.rate).toFixed(2)}
                    </div>
                    {livePrice.change && (
                      <div className={`text-sm ${
                        parseFloat(livePrice.change) >= 0 ? 'text-green-500' : 'text-red-500'
                      }`}>
                        {livePrice.change_percent || `${(parseFloat(livePrice.change) * 100).toFixed(2)}%`}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Chart */}
            <div className={`p-4 rounded-lg ${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'}`}>
              {/* Option 1: TradingView Widget */}
              <TradingChart symbol={`NASDAQ:${selectedAsset}`} />
              
              {/* Option 2: Custom Lightweight Chart */}
              {/* <LightweightChart symbol={selectedAsset} theme={theme} /> */}
            </div>

            {/* Trading Panel */}
            <div className={`p-4 rounded-lg mt-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <h3 className="text-lg font-semibold mb-4">Place Order</h3>
              <div className="grid grid-cols-2 gap-4">
                <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                  BUY
                </button>
                <button className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
                  SELL
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## Next Steps

1. **Choose Your Integration Method**:
   - **Quick Start**: Use TradingView Widgets (Option 1)
   - **Custom Data**: Use Lightweight Charts (Option 3)
   - **Full Control**: Use TradingView Charting Library (Option 2)

2. **Implement Real-time Updates**:
   - WebSocket connection for live prices
   - Polling mechanism for price updates
   - Push notifications for price alerts

3. **Add Advanced Features**:
   - Technical indicators
   - Drawing tools
   - Multiple timeframes
   - Portfolio tracking
   - Order management

4. **Testing**:
   - Test with different asset types
   - Verify real-time data flow
   - Check mobile responsiveness

Your backend is now ready with real Alpha Vantage data. Choose the frontend integration method that best fits your needs!
