import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export interface Activity {
  id: string;
  actor: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  target_user?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  } | null;
  activity_type: string;
  description: string;
  created_at: string;
  ip_address?: string | null;
  action?: string;
}

export interface UserActivity extends Activity {
  action: string;
}

export interface ActivityResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Activity[];
  total_pages: number;
}

export interface ActivityQueryParams {
  page?: number;
  filter?: string;
  limit?: number;
}

// Get user activities
export const getUserActivities = async (
  token: string,
  params: ActivityQueryParams = {}
): Promise<ActivityResponse> => {
  try {
    const { page = 1, filter = 'all', limit = 10 } = params;
    let url = `${API_URL}/activities/?page=${page}&limit=${limit}`;

    if (filter !== 'all') {
      url += `&filter=${filter}`;
    }

    // Mock data for development
    const mockActivities: Activity[] = [
      {
        id: '1',
        actor: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        },
        activity_type: 'LOGIN',
        description: 'Logged in from Chrome on Windows',
        ip_address: '***********',
        created_at: new Date().toISOString(),
        action: 'login'
      },
      {
        id: '2',
        actor: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        },
        activity_type: 'DEPOSIT',
        description: 'Deposited $1000 to Main USD Wallet',
        ip_address: '***********',
        created_at: new Date(Date.now() - 3600000).toISOString(),
        action: 'deposit'
      },
      {
        id: '3',
        actor: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        },
        activity_type: 'WITHDRAWAL',
        description: 'Withdrew $500 from Main USD Wallet',
        ip_address: '***********',
        created_at: new Date(Date.now() - 7200000).toISOString(),
        action: 'withdraw'
      },
      {
        id: '4',
        actor: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        },
        activity_type: 'TRADE',
        description: 'Opened BUY position for BTC/USD',
        ip_address: '***********',
        created_at: new Date(Date.now() - 10800000).toISOString(),
        action: 'trade'
      },
      {
        id: '5',
        actor: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        },
        activity_type: 'PROFILE_UPDATE',
        description: 'Updated profile information',
        ip_address: '***********',
        created_at: new Date(Date.now() - 14400000).toISOString(),
        action: 'profile'
      },
    ];

    // Filter activities based on filter parameter
    let filteredActivities = [...mockActivities];
    if (filter === 'login') {
      filteredActivities = mockActivities.filter(a => a.activity_type === 'LOGIN' || a.activity_type === 'LOGOUT');
    } else if (filter === 'financial') {
      filteredActivities = mockActivities.filter(a => a.activity_type === 'DEPOSIT' || a.activity_type === 'WITHDRAWAL');
    } else if (filter === 'trade') {
      filteredActivities = mockActivities.filter(a => a.activity_type === 'TRADE');
    } else if (filter === 'security') {
      filteredActivities = mockActivities.filter(a => a.activity_type === 'SECURITY_UPDATE');
    }

    const mockResponse: ActivityResponse = {
      count: filteredActivities.length,
      next: null,
      previous: null,
      results: filteredActivities,
      total_pages: Math.ceil(filteredActivities.length / limit),
    };

    return mockResponse;

    // Uncomment for real API call
    // const response = await axios.get(url, {
    //   headers: {
    //     Authorization: `Token ${token}`,
    //   },
    // });
    //
    // // If backend doesn't provide total_pages, calculate it
    // if (!response.data.total_pages && response.data.count) {
    //   response.data.total_pages = Math.ceil(response.data.count / limit);
    // }
    //
    // return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get user activities');
    }
    throw new Error('Network error. Please try again.');
  }
};
