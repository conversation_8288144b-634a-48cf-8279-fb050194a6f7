import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export interface AlphaVantageAsset {
  symbol: string;
  name: string;
  type: string;
}

export interface LiveQuote {
  symbol: string;
  price: string;
  change: string;
  change_percent: string;
  volume?: string;
  high?: string;
  low?: string;
  open?: string;
  previous_close?: string;
  last_updated: string;
}

export interface PriceHistoryItem {
  timestamp: string;
  price: string;
  open?: string;
  high?: string;
  low?: string;
  volume?: string;
}

export interface SupportedAssetsResponse {
  current_assets: {
    [key: string]: AlphaVantageAsset[];
  };
  total_count: number;
}

export interface LiveQuoteResponse {
  symbol: string;
  live_data: LiveQuote;
  status: string;
}

export interface PriceHistoryResponse {
  symbol: string;
  price_history: PriceHistoryItem[];
  days_requested: number;
  status: string;
}

// Get supported assets from Alpha Vantage
export const getSupportedAssets = async (token?: string): Promise<SupportedAssetsResponse> => {
  try {
    const headers: any = {};
    if (token) {
      headers.Authorization = `Token ${token}`;
    }

    const response = await axios.get(`${API_URL}/alpha-vantage/supported_assets/`, {
      headers,
    });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching supported assets:', error);
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to fetch supported assets');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Get live quote for a specific symbol
export const getLiveQuote = async (symbol: string, token?: string): Promise<LiveQuoteResponse> => {
  try {
    const headers: any = {};
    if (token) {
      headers.Authorization = `Token ${token}`;
    }

    const response = await axios.get(`${API_URL}/alpha-vantage/live_quote/`, {
      params: { symbol },
      headers,
    });
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching live quote for ${symbol}:`, error);
    if (error.response) {
      throw new Error(error.response.data.detail || `Failed to fetch live quote for ${symbol}`);
    }
    throw new Error('Network error. Please try again.');
  }
};

// Get price history for a specific symbol
export const getPriceHistory = async (
  symbol: string,
  days: number = 30,
  token?: string
): Promise<PriceHistoryResponse> => {
  try {
    const headers: any = {};
    if (token) {
      headers.Authorization = `Token ${token}`;
    }

    const response = await axios.get(`${API_URL}/alpha-vantage/price_history/`, {
      params: { symbol, days },
      headers,
    });
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching price history for ${symbol}:`, error);
    if (error.response) {
      throw new Error(error.response.data.detail || `Failed to fetch price history for ${symbol}`);
    }
    throw new Error('Network error. Please try again.');
  }
};

// Refresh prices (trigger manual update)
export const refreshPrices = async (token?: string): Promise<{ status: string; message: string }> => {
  try {
    const headers: any = {};
    if (token) {
      headers.Authorization = `Token ${token}`;
    }

    const response = await axios.post(`${API_URL}/alpha-vantage/refresh_prices/`, {}, {
      headers,
    });
    return response.data;
  } catch (error: any) {
    console.error('Error refreshing prices:', error);
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to refresh prices');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Convert Alpha Vantage asset to our Asset interface
export const convertAlphaVantageAsset = (
  alphaAsset: AlphaVantageAsset,
  liveQuote?: LiveQuote
): import('./trading').Asset => {
  return {
    id: alphaAsset.symbol.toLowerCase().replace(/[^a-z0-9]/g, '-'),
    symbol: alphaAsset.symbol,
    name: alphaAsset.name,
    asset_type: alphaAsset.type.toUpperCase(),
    current_price: liveQuote?.price || '0',
    price_change_24h: liveQuote?.change || '0',
    price_change_percentage_24h: liveQuote?.change_percent || '0',
    last_updated: liveQuote?.last_updated || new Date().toISOString(),
  };
};

// Batch fetch live quotes for multiple symbols
export const getBatchLiveQuotes = async (
  symbols: string[],
  token?: string
): Promise<{ [symbol: string]: LiveQuote }> => {
  try {
    const quotes: { [symbol: string]: LiveQuote } = {};
    
    // Fetch quotes in parallel but limit concurrent requests
    const batchSize = 5;
    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize);
      const promises = batch.map(symbol => 
        getLiveQuote(symbol, token).catch(error => {
          console.warn(`Failed to fetch quote for ${symbol}:`, error.message);
          return null;
        })
      );
      
      const results = await Promise.all(promises);
      results.forEach((result, index) => {
        if (result && result.live_data) {
          quotes[batch[index]] = result.live_data;
        }
      });
    }
    
    return quotes;
  } catch (error: any) {
    console.error('Error fetching batch live quotes:', error);
    throw new Error('Failed to fetch live quotes');
  }
};

// Real-time price subscription (polling-based)
export class PriceSubscription {
  private intervals: { [symbol: string]: NodeJS.Timeout } = {};
  private callbacks: { [symbol: string]: ((quote: LiveQuote) => void)[] } = {};

  subscribe(symbol: string, callback: (quote: LiveQuote) => void, intervalMs: number = 30000, token?: string) {
    if (!this.callbacks[symbol]) {
      this.callbacks[symbol] = [];
    }
    
    this.callbacks[symbol].push(callback);

    // Start polling if this is the first subscription for this symbol
    if (!this.intervals[symbol]) {
      const poll = async () => {
        try {
          const response = await getLiveQuote(symbol, token);
          if (response.live_data) {
            this.callbacks[symbol]?.forEach(cb => cb(response.live_data));
          }
        } catch (error) {
          console.warn(`Error polling price for ${symbol}:`, error);
        }
      };

      // Initial fetch
      poll();
      
      // Set up interval
      this.intervals[symbol] = setInterval(poll, intervalMs);
    }
  }

  unsubscribe(symbol: string, callback?: (quote: LiveQuote) => void) {
    if (callback && this.callbacks[symbol]) {
      this.callbacks[symbol] = this.callbacks[symbol].filter(cb => cb !== callback);
      
      // If no more callbacks, stop polling
      if (this.callbacks[symbol].length === 0) {
        this.stopPolling(symbol);
      }
    } else {
      // Unsubscribe all callbacks for this symbol
      this.stopPolling(symbol);
    }
  }

  private stopPolling(symbol: string) {
    if (this.intervals[symbol]) {
      clearInterval(this.intervals[symbol]);
      delete this.intervals[symbol];
    }
    delete this.callbacks[symbol];
  }

  unsubscribeAll() {
    Object.keys(this.intervals).forEach(symbol => this.stopPolling(symbol));
  }
}

// Global price subscription instance
export const priceSubscription = new PriceSubscription();
