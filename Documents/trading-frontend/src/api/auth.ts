import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  account_type: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  last_login: string;
  role: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export const login = async (data: LoginData): Promise<AuthResponse> => {
  try {
    const response = await axios.post(`${API_URL}/auth/login/`, data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Login failed');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const register = async (data: RegisterData): Promise<AuthResponse> => {
  try {
    const response = await axios.post(`${API_URL}/auth/register/`, data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Registration failed');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const logout = async (token: string): Promise<void> => {
  try {
    await axios.post(
      `${API_URL}/auth/logout/`,
      {},
      {
        headers: {
          Authorization: `Token ${token}`,
        },
      }
    );
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Logout failed');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getUser = async (token: string): Promise<User> => {
  try {
    const response = await axios.get(`${API_URL}/auth/user/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get user data');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const resetPassword = async (email: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/auth/password-reset/`, { email });
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Password reset failed');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const confirmResetPassword = async (
  token: string,
  password: string
): Promise<void> => {
  try {
    await axios.post(`${API_URL}/auth/password-reset/confirm/`, {
      token,
      password,
    });
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Password reset confirmation failed');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const verifyEmail = async (token: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/auth/verify-email/`, { token });
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Email verification failed');
    }
    throw new Error('Network error. Please try again.');
  }
};
