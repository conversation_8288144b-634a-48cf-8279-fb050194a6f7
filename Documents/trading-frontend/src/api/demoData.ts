// Demo data for testing the trading interface without backend
import { Asset } from './trading';
import { 
  SupportedAssetsResponse, 
  LiveQuoteResponse, 
  PriceHistoryResponse,
  AlphaVantageAsset,
  LiveQuote,
  PriceHistoryItem
} from './alphaVantage';

// Demo assets data
const demoAlphaVantageAssets: { [key: string]: AlphaVantageAsset[] } = {
  CRYPTO: [
    { symbol: 'BTC/USD', name: 'Bitcoin', type: 'CRYPTO' },
    { symbol: 'ETH/USD', name: 'Ethereum', type: 'CRYPTO' },
    { symbol: 'SOL/USD', name: 'Solana', type: 'CRYPTO' },
    { symbol: 'ADA/USD', name: 'Cardano', type: 'CRYPTO' },
    { symbol: 'DOT/USD', name: '<PERSON><PERSON><PERSON>', type: 'CRYPTO' },
  ],
  STOCK: [
    { symbol: 'AAPL', name: 'Apple Inc.', type: 'STOCK' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.', type: 'STOCK' },
    { symbol: 'MSFT', name: 'Microsoft Corporation', type: 'STOCK' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.', type: 'STOCK' },
    { symbol: 'TSLA', name: 'Tesla Inc.', type: 'STOCK' },
    { symbol: 'NVDA', name: 'NVIDIA Corporation', type: 'STOCK' },
  ],
  FOREX: [
    { symbol: 'EUR/USD', name: 'Euro to US Dollar', type: 'FOREX' },
    { symbol: 'GBP/USD', name: 'British Pound to US Dollar', type: 'FOREX' },
    { symbol: 'USD/JPY', name: 'US Dollar to Japanese Yen', type: 'FOREX' },
    { symbol: 'AUD/USD', name: 'Australian Dollar to US Dollar', type: 'FOREX' },
  ],
  COMMODITY: [
    { symbol: 'GOLD', name: 'Gold', type: 'COMMODITY' },
    { symbol: 'SILVER', name: 'Silver', type: 'COMMODITY' },
    { symbol: 'OIL', name: 'Crude Oil', type: 'COMMODITY' },
  ],
};

// Generate random price data
const generateRandomPrice = (basePrice: number, volatility: number = 0.05) => {
  const change = (Math.random() - 0.5) * 2 * volatility;
  return basePrice * (1 + change);
};

const generatePriceChange = () => {
  const change = (Math.random() - 0.5) * 10; // -5% to +5%
  return {
    change: change.toFixed(2),
    change_percent: change.toFixed(2),
  };
};

// Base prices for different assets
const basePrices: { [symbol: string]: number } = {
  'BTC/USD': 43000,
  'ETH/USD': 2600,
  'SOL/USD': 95,
  'ADA/USD': 0.45,
  'DOT/USD': 7.2,
  'AAPL': 195,
  'GOOGL': 140,
  'MSFT': 420,
  'AMZN': 155,
  'TSLA': 240,
  'NVDA': 875,
  'EUR/USD': 1.08,
  'GBP/USD': 1.27,
  'USD/JPY': 149.5,
  'AUD/USD': 0.66,
  'GOLD': 2050,
  'SILVER': 24.5,
  'OIL': 78.5,
};

// Demo API functions
export const getDemoSupportedAssets = async (): Promise<SupportedAssetsResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const totalCount = Object.values(demoAlphaVantageAssets)
    .reduce((sum, assets) => sum + assets.length, 0);

  return {
    current_assets: demoAlphaVantageAssets,
    total_count: totalCount,
  };
};

export const getDemoLiveQuote = async (symbol: string): Promise<LiveQuoteResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const basePrice = basePrices[symbol] || 100;
  const currentPrice = generateRandomPrice(basePrice);
  const { change, change_percent } = generatePriceChange();
  
  const liveQuote: LiveQuote = {
    symbol,
    price: currentPrice.toFixed(2),
    change,
    change_percent,
    volume: Math.floor(Math.random() * 1000000).toString(),
    high: (currentPrice * 1.02).toFixed(2),
    low: (currentPrice * 0.98).toFixed(2),
    open: (currentPrice * 0.995).toFixed(2),
    previous_close: (currentPrice - parseFloat(change)).toFixed(2),
    last_updated: new Date().toISOString(),
  };

  return {
    symbol,
    live_data: liveQuote,
    status: 'success',
  };
};

export const getDemoPriceHistory = async (
  symbol: string, 
  days: number = 30
): Promise<PriceHistoryResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const basePrice = basePrices[symbol] || 100;
  const priceHistory: PriceHistoryItem[] = [];
  
  // Generate historical data
  for (let i = days; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    const price = generateRandomPrice(basePrice, 0.02);
    const open = generateRandomPrice(price, 0.01);
    const high = Math.max(price, open) * (1 + Math.random() * 0.01);
    const low = Math.min(price, open) * (1 - Math.random() * 0.01);
    
    priceHistory.push({
      timestamp: date.toISOString(),
      price: price.toFixed(2),
      open: open.toFixed(2),
      high: high.toFixed(2),
      low: low.toFixed(2),
      volume: Math.floor(Math.random() * 1000000).toString(),
    });
  }

  return {
    symbol,
    price_history: priceHistory,
    days_requested: days,
    status: 'success',
  };
};

export const getDemoBatchLiveQuotes = async (
  symbols: string[]
): Promise<{ [symbol: string]: LiveQuote }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const quotes: { [symbol: string]: LiveQuote } = {};
  
  for (const symbol of symbols) {
    try {
      const response = await getDemoLiveQuote(symbol);
      quotes[symbol] = response.live_data;
    } catch (error) {
      console.warn(`Failed to get demo quote for ${symbol}`);
    }
  }
  
  return quotes;
};

// Check if we should use demo data (when backend is not available)
export const shouldUseDemoData = () => {
  // You can add logic here to detect if backend is available
  // For now, we'll use demo data if NEXT_PUBLIC_USE_DEMO_DATA is set
  return process.env.NEXT_PUBLIC_USE_DEMO_DATA === 'true';
};

// Wrapper functions that choose between real API and demo data
export const getAssetsData = async (token?: string) => {
  if (shouldUseDemoData()) {
    console.log('Using demo data for assets');
    return getDemoSupportedAssets();
  }

  // Import the real API function dynamically to avoid errors if backend is down
  try {
    const { createTradingService } = await import('./tradingService');
    if (!token) throw new Error('Token required for real API');

    const tradingService = createTradingService(token);
    const assets = await tradingService.getAssetsWithLivePrices();

    // Convert to Alpha Vantage format for compatibility
    const assetsByType: { [key: string]: any[] } = {};
    assets.forEach(asset => {
      if (!assetsByType[asset.asset_type]) {
        assetsByType[asset.asset_type] = [];
      }
      assetsByType[asset.asset_type].push({
        symbol: asset.symbol,
        name: asset.name,
        type: asset.asset_type,
      });
    });

    return {
      current_assets: assetsByType,
      total_count: assets.length,
    };
  } catch (error) {
    console.warn('Backend not available, falling back to demo data');
    return getDemoSupportedAssets();
  }
};

export const getLiveQuoteData = async (symbol: string, token?: string) => {
  if (shouldUseDemoData()) {
    console.log(`Using demo data for live quote: ${symbol}`);
    return getDemoLiveQuote(symbol);
  }

  try {
    const { createTradingService } = await import('./tradingService');
    if (!token) throw new Error('Token required for real API');

    const tradingService = createTradingService(token);
    const liveData = await tradingService.getLivePrice(symbol);

    // Convert to Alpha Vantage format for compatibility
    return {
      symbol,
      live_data: {
        symbol: liveData.asset.symbol,
        price: liveData.live_data.price,
        change: liveData.live_data.change,
        change_percent: liveData.live_data.change_percent,
        volume: liveData.live_data.volume?.toString() || '0',
        high: liveData.live_data.high,
        low: liveData.live_data.low,
        open: liveData.live_data.open,
        previous_close: (parseFloat(liveData.live_data.price) - parseFloat(liveData.live_data.change)).toFixed(2),
        last_updated: liveData.timestamp,
      },
      status: 'success',
    };
  } catch (error) {
    console.warn(`Backend not available for ${symbol}, falling back to demo data`);
    return getDemoLiveQuote(symbol);
  }
};

export const getPriceHistoryData = async (
  symbol: string,
  days: number = 30,
  token?: string
) => {
  if (shouldUseDemoData()) {
    console.log(`Using demo data for price history: ${symbol}`);
    return getDemoPriceHistory(symbol, days);
  }

  try {
    const { createTradingService } = await import('./tradingService');
    if (!token) throw new Error('Token required for real API');

    const tradingService = createTradingService(token);
    return tradingService.getPriceHistory(symbol, days);
  } catch (error) {
    console.warn(`Backend not available for ${symbol} history, falling back to demo data`);
    return getDemoPriceHistory(symbol, days);
  }
};

export const getBatchLiveQuotesData = async (
  symbols: string[],
  token?: string
) => {
  if (shouldUseDemoData()) {
    console.log('Using demo data for batch live quotes');
    return getDemoBatchLiveQuotes(symbols);
  }

  try {
    const { createTradingService } = await import('./tradingService');
    if (!token) throw new Error('Token required for real API');

    const tradingService = createTradingService(token);
    const liveDataResults = await tradingService.getBatchLivePrices(symbols);

    // Convert to the expected format
    const quotes: { [symbol: string]: any } = {};
    Object.entries(liveDataResults).forEach(([symbol, liveData]) => {
      quotes[symbol] = {
        symbol: liveData.asset.symbol,
        price: liveData.live_data.price,
        change: liveData.live_data.change,
        change_percent: liveData.live_data.change_percent,
        volume: liveData.live_data.volume?.toString() || '0',
        high: liveData.live_data.high,
        low: liveData.live_data.low,
        open: liveData.live_data.open,
        previous_close: (parseFloat(liveData.live_data.price) - parseFloat(liveData.live_data.change)).toFixed(2),
        last_updated: liveData.timestamp,
      };
    });

    return quotes;
  } catch (error) {
    console.warn('Backend not available for batch quotes, falling back to demo data');
    return getDemoBatchLiveQuotes(symbols);
  }
};
