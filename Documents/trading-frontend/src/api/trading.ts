import axios from 'axios';
import { TradingAccount } from './wallets';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export interface Asset {
  id: string;
  symbol: string;
  name: string;
  asset_type: string;
  current_price: string;
  price_change_24h: string;
  price_change_percentage_24h: string;
  last_updated: string;
}

export interface TradingPosition {
  id: string;
  account_id: string;
  symbol: string;
  position_type: string;
  volume: string;
  open_price: string;
  current_price: string;
  profit_loss: string;
  created_at: string;
}

export interface Trade {
  id: string;
  user: string;
  account: TradingAccount;
  asset: Asset;
  trade_type: string;
  direction: string;
  status: string;
  volume: string;
  open_price: string;
  close_price: string | null;
  take_profit: string | null;
  stop_loss: string | null;
  leverage: number;
  margin: string;
  profit_loss: string;
  open_time: string;
  close_time: string | null;
}

export interface TradeCreateData {
  account_id: string;
  asset_id: string;
  trade_type: string;
  direction: string;
  volume: string;
  leverage: number;
  take_profit?: string;
  stop_loss?: string;
}

export interface TradeUpdateData {
  take_profit?: string;
  stop_loss?: string;
}

export interface TradingAccountCreateData {
  name: string;
  leverage: number;
  wallet_id: string;
}

export interface FundAccountData {
  account_id: string;
  wallet_id: string;
  amount: string;
}

// Asset API functions
export const getAssets = async (token: string, assetType?: string, isActive: boolean = true): Promise<Asset[]> => {
  try {
    const params = new URLSearchParams();
    if (assetType) params.append('asset_type', assetType);
    if (isActive) params.append('is_active', 'true');

    const url = `${API_URL}/assets/${params.toString() ? '?' + params.toString() : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get assets');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getAsset = async (token: string, id: string): Promise<Asset> => {
  try {
    const response = await axios.get(`${API_URL}/assets/${id}/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get asset');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Get asset prices with optional date range
export const getAssetPrices = async (
  token: string,
  assetId?: string,
  startDate?: string,
  endDate?: string
): Promise<any[]> => {
  try {
    const params = new URLSearchParams();
    if (assetId) params.append('asset_id', assetId);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const url = `${API_URL}/asset-prices/${params.toString() ? '?' + params.toString() : ''}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get asset prices');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Trade API functions
export const getTrades = async (
  token: string,
  accountId?: string,
  status?: string
): Promise<Trade[]> => {
  try {
    let url = `${API_URL}/trades/`;
    const params = [];

    if (accountId) {
      params.push(`account_id=${accountId}`);
    }

    if (status) {
      params.push(`status=${status}`);
    }

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    const response = await axios.get(url, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get trades');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getTrade = async (token: string, id: string): Promise<Trade> => {
  try {
    const response = await axios.get(`${API_URL}/trades/${id}/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get trade');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const createTrade = async (token: string, data: TradeCreateData): Promise<Trade> => {
  try {
    const response = await axios.post(`${API_URL}/trades/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to create trade');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const updateTrade = async (
  token: string,
  id: string,
  data: TradeUpdateData
): Promise<Trade> => {
  try {
    const response = await axios.patch(`${API_URL}/trades/${id}/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to update trade');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const closeTrade = async (token: string, id: string): Promise<Trade> => {
  try {
    const response = await axios.post(
      `${API_URL}/trades/${id}/close/`,
      {},
      {
        headers: {
          Authorization: `Token ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to close trade');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Trading Account API functions
export const getTradingAccounts = async (token: string): Promise<TradingAccount[]> => {
  try {
    const response = await axios.get(`${API_URL}/trading-accounts/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get trading accounts');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getTradingAccount = async (token: string, id: string): Promise<TradingAccount> => {
  try {
    const response = await axios.get(`${API_URL}/trading-accounts/${id}/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get trading account');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const createTradingAccount = async (
  token: string,
  data: TradingAccountCreateData
): Promise<TradingAccount> => {
  try {
    const response = await axios.post(`${API_URL}/trading-accounts/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to create trading account');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const fundAccount = async (
  token: string,
  data: FundAccountData
): Promise<TradingAccount> => {
  try {
    const response = await axios.post(`${API_URL}/trading-accounts/fund/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to fund account');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getTradingPositions = async (
  token: string,
  accountId: string
): Promise<TradingPosition[]> => {
  try {
    const response = await axios.get(`${API_URL}/trading-accounts/${accountId}/positions/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get positions');
    }
    throw new Error('Network error. Please try again.');
  }
};
