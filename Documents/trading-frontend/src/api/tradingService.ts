import axios from 'axios';
import { Asset } from './trading';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export interface EnhancedAsset extends Asset {
  description?: string;
  data_source?: string;
  last_price_update?: string;
  is_active?: boolean;
}

export interface LivePriceData {
  asset: {
    symbol: string;
    name: string;
    asset_type: string;
  };
  live_data: {
    price: string;
    open: string;
    high: string;
    low: string;
    volume: number;
    change: string;
    change_percent: string;
  };
  source: string;
  timestamp: string;
}

export interface AssetPriceHistory {
  id: string;
  asset: string;
  price: string;
  open_price: string;
  high_price: string;
  low_price: string;
  volume: number;
  change: string;
  change_percent: string;
  data_source: string;
  timestamp: string;
}

// Enhanced Trading Service that combines regular assets API with Alpha Vantage
export class TradingService {
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  private getHeaders() {
    return {
      'Authorization': `Token ${this.token}`,
      'Content-Type': 'application/json',
    };
  }

  // Get all assets from the main assets endpoint (175 total)
  async getAllAssets(assetType?: string): Promise<EnhancedAsset[]> {
    try {
      const params = new URLSearchParams();
      if (assetType) params.append('asset_type', assetType);
      params.append('is_active', 'true');
      
      const url = `${API_URL}/assets/${params.toString() ? '?' + params.toString() : ''}`;

      const response = await axios.get(url, {
        headers: this.getHeaders(),
      });

      return response.data.map((asset: any) => ({
        id: asset.id,
        symbol: asset.symbol,
        name: asset.name,
        asset_type: asset.asset_type,
        current_price: '0', // Will be updated with live data
        price_change_24h: '0',
        price_change_percentage_24h: '0',
        last_updated: asset.last_price_update || new Date().toISOString(),
        description: asset.description,
        data_source: asset.data_source,
        is_active: asset.is_active,
      }));
    } catch (error: any) {
      console.error('Error fetching assets:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch assets');
    }
  }

  // Get live price data from Alpha Vantage endpoint
  async getLivePrice(symbol: string): Promise<LivePriceData> {
    try {
      const response = await axios.get(`${API_URL}/alpha-vantage/live_quote/`, {
        params: { symbol },
        headers: this.getHeaders(),
      });

      return response.data;
    } catch (error: any) {
      console.error(`Error fetching live price for ${symbol}:`, error);
      throw new Error(error.response?.data?.detail || `Failed to fetch live price for ${symbol}`);
    }
  }

  // Get price history from Alpha Vantage endpoint
  async getPriceHistory(symbol: string, days: number = 30): Promise<any> {
    try {
      const response = await axios.get(`${API_URL}/alpha-vantage/price_history/`, {
        params: { symbol, days },
        headers: this.getHeaders(),
      });

      return response.data;
    } catch (error: any) {
      console.error(`Error fetching price history for ${symbol}:`, error);
      throw new Error(error.response?.data?.detail || `Failed to fetch price history for ${symbol}`);
    }
  }

  // Get supported assets from Alpha Vantage (for real-time data)
  async getSupportedAlphaVantageAssets(): Promise<any> {
    try {
      const response = await axios.get(`${API_URL}/alpha-vantage/supported_assets/`, {
        headers: this.getHeaders(),
      });

      return response.data;
    } catch (error: any) {
      console.error('Error fetching Alpha Vantage assets:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch Alpha Vantage assets');
    }
  }

  // Get assets with live prices (combines both APIs)
  async getAssetsWithLivePrices(assetType?: string): Promise<EnhancedAsset[]> {
    try {
      // Get all assets from main API
      const assets = await this.getAllAssets(assetType);
      
      // Get Alpha Vantage supported assets to know which ones have live data
      const alphaVantageData = await this.getSupportedAlphaVantageAssets();
      const supportedSymbols = new Set<string>();
      
      Object.values(alphaVantageData.current_assets).forEach((assetList: any) => {
        assetList.forEach((asset: any) => {
          supportedSymbols.add(asset.symbol);
        });
      });

      // Update assets with live prices where available
      const updatedAssets = await Promise.all(
        assets.map(async (asset) => {
          if (supportedSymbols.has(asset.symbol)) {
            try {
              const liveData = await this.getLivePrice(asset.symbol);
              return {
                ...asset,
                current_price: liveData.live_data.price,
                price_change_24h: liveData.live_data.change,
                price_change_percentage_24h: liveData.live_data.change_percent,
                last_updated: liveData.timestamp,
              };
            } catch (error) {
              console.warn(`Failed to get live price for ${asset.symbol}:`, error);
              return asset;
            }
          }
          return asset;
        })
      );

      return updatedAssets;
    } catch (error: any) {
      console.error('Error getting assets with live prices:', error);
      throw error;
    }
  }

  // Batch get live prices for multiple symbols
  async getBatchLivePrices(symbols: string[]): Promise<{ [symbol: string]: LivePriceData }> {
    const results: { [symbol: string]: LivePriceData } = {};
    
    // Process in batches to avoid overwhelming the API
    const batchSize = 5;
    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize);
      const promises = batch.map(symbol => 
        this.getLivePrice(symbol).catch(error => {
          console.warn(`Failed to fetch price for ${symbol}:`, error.message);
          return null;
        })
      );
      
      const batchResults = await Promise.all(promises);
      batchResults.forEach((result, index) => {
        if (result) {
          results[batch[index]] = result;
        }
      });
    }
    
    return results;
  }

  // Get asset price history from the main API
  async getAssetPriceHistory(
    assetId?: string,
    startDate?: string,
    endDate?: string
  ): Promise<AssetPriceHistory[]> {
    try {
      const params = new URLSearchParams();
      if (assetId) params.append('asset_id', assetId);
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);
      
      const url = `${API_URL}/asset-prices/${params.toString() ? '?' + params.toString() : ''}`;

      const response = await axios.get(url, {
        headers: this.getHeaders(),
      });

      return response.data;
    } catch (error: any) {
      console.error('Error fetching asset price history:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch asset price history');
    }
  }

  // Refresh prices manually (admin only)
  async refreshPrices(): Promise<any> {
    try {
      const response = await axios.post(`${API_URL}/alpha-vantage/refresh_prices/`, {}, {
        headers: this.getHeaders(),
      });

      return response.data;
    } catch (error: any) {
      console.error('Error refreshing prices:', error);
      throw new Error(error.response?.data?.detail || 'Failed to refresh prices');
    }
  }
}

// Utility function to create a trading service instance
export const createTradingService = (token: string) => {
  return new TradingService(token);
};

// Export types for use in components
export type { EnhancedAsset, LivePriceData, AssetPriceHistory };
