import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export interface Wallet {
  id: string;
  user: string;
  name: string;
  currency: string;
  balance: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TradingAccount {
  id: string;
  user: string;
  wallet: Wallet;
  name: string;
  account_number: string;
  currency: string;
  balance: string;
  leverage: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface WalletCreateData {
  name: string;
  currency: string;
}

export interface TradingAccountCreateData {
  name: string;
  wallet_id: string;
  leverage: number;
}

export interface TransactionCreateData {
  wallet_id: string;
  transaction_type: string;
  amount: string;
  payment_method?: string;
  description?: string;
}

export interface Transaction {
  id: string;
  wallet: Wallet;
  transaction_type: string;
  amount: string;
  payment_method: string;
  status: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface TransferData {
  from_wallet_id?: string;
  to_wallet_id?: string;
  from_account_id?: string;
  to_account_id?: string;
  amount: string;
}

export interface PaginatedWallets {
  count: number;
  next: string | null;
  previous: string | null;
  results: Wallet[];
}

// Wallet API functions
export const getWallets = async (token: string): Promise<Wallet[] | PaginatedWallets> => {
  try {
    // Log the token and Authorization header for debugging
    console.log('getWallets: token being sent:', token);
    console.log('getWallets: Authorization header:', `Token ${token}`);
    const response = await axios.get(`${API_URL}/wallets/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    console.log("wallets",response.data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get wallets');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getWallet = async (token: string, id: string): Promise<Wallet | Wallet[]> => {
  try {
    // Mock data for development
    /*
    const mockWallets: Wallet[] = [
      {
        id: '1',
        user: 'user1',
        name: 'Main USD Wallet',
        currency: 'USD',
        balance: '10000.00',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '2',
        user: 'user1',
        name: 'EUR Wallet',
        currency: 'EUR',
        balance: '5000.00',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '3',
        user: 'user1',
        name: 'BTC Wallet',
        currency: 'BTC',
        balance: '0.5',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // If id is empty, return all wallets
    if (!id) {
      return mockWallets;
    }

    // Find the wallet with the matching id
    const wallet = mockWallets.find(w => w.id === id);
    if (!wallet) {
      throw new Error('Wallet not found');
    }

    return wallet;
    */

    // Uncomment for real API call
    const response = await axios.get(`${API_URL}/wallets/${id}/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    // console.log(response.data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get wallet');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const createWallet = async (token: string, data: WalletCreateData): Promise<Wallet> => {
  try {
    const response = await axios.post(`${API_URL}/wallets/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to create wallet');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Trading Account API functions
export const getTradingAccounts = async (token: string): Promise<TradingAccount[]> => {
  try {
    const response = await axios.get(`${API_URL}/trading-accounts/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get trading accounts');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const getTradingAccount = async (token: string, id: string): Promise<TradingAccount> => {
  try {
    const response = await axios.get(`${API_URL}/trading-accounts/${id}/`, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get trading account');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const createTradingAccount = async (
  token: string,
  data: TradingAccountCreateData
): Promise<TradingAccount> => {
  try {
    const response = await axios.post(`${API_URL}/trading-accounts/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to create trading account');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Transaction API functions
export const getTransactions = async (token: string, walletId?: string): Promise<Transaction[]> => {
  try {
    // Mock data for development
    const mockTransactions: Transaction[] = [
      {
        id: '1',
        wallet: {
          id: walletId || '1',
          user: 'user1',
          name: 'Main USD Wallet',
          currency: 'USD',
          balance: '10000.00',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        transaction_type: 'DEPOSIT',
        amount: '1000.00',
        payment_method: 'CREDIT_CARD',
        status: 'COMPLETED',
        description: 'Initial deposit',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '2',
        wallet: {
          id: walletId || '1',
          user: 'user1',
          name: 'Main USD Wallet',
          currency: 'USD',
          balance: '10000.00',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        transaction_type: 'WITHDRAWAL',
        amount: '500.00',
        payment_method: 'BANK_TRANSFER',
        status: 'COMPLETED',
        description: 'Withdrawal to bank account',
        created_at: new Date(Date.now() - ********).toISOString(), // 1 day ago
        updated_at: new Date(Date.now() - ********).toISOString(),
      },
      {
        id: '3',
        wallet: {
          id: walletId || '1',
          user: 'user1',
          name: 'Main USD Wallet',
          currency: 'USD',
          balance: '10000.00',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        transaction_type: 'TRANSFER',
        amount: '200.00',
        payment_method: 'INTERNAL',
        status: 'COMPLETED',
        description: 'Transfer to EUR wallet',
        created_at: new Date(Date.now() - *********).toISOString(), // 2 days ago
        updated_at: new Date(Date.now() - *********).toISOString(),
      },
    ];

    // Filter transactions by wallet ID if provided
    if (walletId) {
      return mockTransactions.filter(t => t.wallet.id === walletId);
    }

    return mockTransactions;

    // Uncomment for real API call
    // const url = walletId
    //   ? `${API_URL}/transactions/?wallet_id=${walletId}`
    //   : `${API_URL}/transactions/`;
    //
    // const response = await axios.get(url, {
    //   headers: {
    //     Authorization: `Token ${token}`,
    //   },
    // });
    // return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to get transactions');
    }
    throw new Error('Network error. Please try again.');
  }
};

export const createTransaction = async (
  token: string,
  data: TransactionCreateData
): Promise<Transaction> => {
  try {
    const response = await axios.post(`${API_URL}/transactions/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to create transaction');
    }
    throw new Error('Network error. Please try again.');
  }
};

// Transfer API functions
export const transferFunds = async (token: string, data: TransferData): Promise<Transaction> => {
  try {
    const response = await axios.post(`${API_URL}/transfers/`, data, {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.detail || 'Failed to transfer funds');
    }
    throw new Error('Network error. Please try again.');
  }
};
