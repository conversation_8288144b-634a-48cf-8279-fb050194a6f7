'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { TradingApiService } from '../../../../lib/api/trading';
import { WalletApiService } from '../../../../lib/api/wallet';
import { TradingAccount, Wallet, Trade } from '../../../../types/api';
import Button from '../../../../components/ui/Button';
import { ArrowLeftIcon, ArrowUpIcon, ArrowDownIcon, ChartBarIcon, BanknotesIcon } from '@heroicons/react/24/outline';

export default function TradingAccountDetailPage() {
  const params = useParams();
  const router = useRouter();
  const accountId = params.id as string;

  const [account, setAccount] = useState<TradingAccount | null>(null);
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Transfer modal states
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false);
  const [transferData, setTransferData] = useState({
    amount: '',
    description: '',
  });
  const [transferLoading, setTransferLoading] = useState(false);

  // Edit modal states
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editData, setEditData] = useState({
    name: '',
    leverage: 1,
  });

  useEffect(() => {
    fetchAccountData();
  }, [accountId]);

  const fetchAccountData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch account details and trades in parallel
      const [accountResponse, tradesResponse] = await Promise.all([
        TradingApiService.getTradingAccount(accountId),
        TradingApiService.getTrades({ account_id: accountId, page_size: 10 }),
      ]);

      if (accountResponse.data) {
        setAccount(accountResponse.data);
        setEditData({
          name: accountResponse.data.name,
          leverage: accountResponse.data.leverage
        });

        // Fetch wallet details
        if (accountResponse.data.wallet) {
          const walletResponse = await WalletApiService.getWallet(accountResponse.data.wallet);
          if (walletResponse.data) {
            setWallet(walletResponse.data);
          }
        }
      }

      if (tradesResponse.data && tradesResponse.data.results) {
        setTrades(tradesResponse.data.results);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load account data');
    } finally {
      setLoading(false);
    }
  };

  const handleTransferToWallet = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!transferData.amount) return;

    try {
      setTransferLoading(true);
      await TradingApiService.transferToWallet(accountId, {
        amount: transferData.amount,
        description: transferData.description || 'Transfer to wallet',
      });

      setIsTransferModalOpen(false);
      setTransferData({ amount: '', description: '' });
      await fetchAccountData(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Transfer failed');
    } finally {
      setTransferLoading(false);
    }
  };

  const handleUpdateAccount = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editData.name.trim()) return;

    try {
      await TradingApiService.updateTradingAccount(accountId, editData);
      setIsEditModalOpen(false);
      await fetchAccountData(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Update failed');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!account) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Account Not Found</h2>
          <Button onClick={() => router.push('/dashboard/accounts')}>
            Back to Accounts
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dashboard/accounts')}
                className="mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Accounts
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {account.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {account.account_number} • Leverage: {account.leverage}x • Created {new Date(account.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setIsEditModalOpen(true)}
              >
                Edit Account
              </Button>
              <Button
                onClick={() => setIsTransferModalOpen(true)}
                disabled={parseFloat(account.balance) <= 0}
              >
                <ArrowDownIcon className="h-4 w-4 mr-2" />
                Transfer to Wallet
              </Button>
            </div>
          </div>
        </div>

        {error && (
          <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  {error}
                </h3>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Account Overview */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Account Overview
              </h2>

              <div className="space-y-4">
                <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600 dark:text-green-400">Trading Balance</p>
                      <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                        {parseFloat(account.balance).toLocaleString()} {wallet?.currency || 'USD'}
                      </p>
                    </div>
                    <BanknotesIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">Status</span>
                    <span className={`font-medium ${account.status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'}`}>
                      {account.status}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-gray-500 dark:text-gray-400">Leverage</span>
                    <span className="font-medium text-gray-900 dark:text-white">{account.leverage}x</span>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-gray-500 dark:text-gray-400">Account Number</span>
                    <span className="font-medium text-gray-900 dark:text-white">{account.account_number}</span>
                  </div>
                  {wallet && (
                    <div className="flex justify-between text-sm mt-2">
                      <span className="text-gray-500 dark:text-gray-400">Linked Wallet</span>
                      <button
                        onClick={() => router.push(`/dashboard/wallets/${wallet.id}`)}
                        className="font-medium text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        {wallet.name}
                      </button>
                    </div>
                  )}
                </div>

                <div className="pt-4 space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push('/trading')}
                  >
                    <ChartBarIcon className="h-4 w-4 mr-2" />
                    Start Trading
                  </Button>
                  {wallet && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => router.push(`/dashboard/wallets/${wallet.id}`)}
                    >
                      <ArrowUpIcon className="h-4 w-4 mr-2" />
                      View Wallet
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Recent Trades */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Recent Trades
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/dashboard/trades')}
                >
                  View All
                </Button>
              </div>

              {trades.length === 0 ? (
                <div className="text-center py-8">
                  <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No trades yet</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Start trading to see your trade history here.
                  </p>
                  <div className="mt-6">
                    <Button onClick={() => router.push('/trading')}>
                      Start Trading
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {trades.slice(0, 5).map((trade) => (
                    <div
                      key={trade.id}
                      className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                    >
                      <div className="flex items-center">
                        <div className={`p-2 rounded-full mr-3 ${
                          trade.trade_type === 'BUY'
                            ? 'bg-green-100 dark:bg-green-900'
                            : 'bg-red-100 dark:bg-red-900'
                        }`}>
                          {trade.trade_type === 'BUY' ? (
                            <ArrowUpIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                          ) : (
                            <ArrowDownIcon className="h-4 w-4 text-red-600 dark:text-red-400" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {trade.trade_type} {trade.asset_symbol}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {trade.order_type} • {new Date(trade.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {parseFloat(trade.volume).toLocaleString()} units
                        </p>
                        <p className={`text-xs ${
                          trade.status === 'COMPLETED' ? 'text-green-600' :
                          trade.status === 'PENDING' ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {trade.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Transfer Modal */}
      {isTransferModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Transfer to Wallet
              </h3>
              <form onSubmit={handleTransferToWallet}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Amount ({wallet?.currency || 'USD'})
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    max={account.balance}
                    className="input-field"
                    value={transferData.amount}
                    onChange={(e) => setTransferData({ ...transferData, amount: e.target.value })}
                    required
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Available: {parseFloat(account.balance).toLocaleString()} {wallet?.currency || 'USD'}
                  </p>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description (Optional)
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    value={transferData.description}
                    onChange={(e) => setTransferData({ ...transferData, description: e.target.value })}
                    placeholder="Transfer description"
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsTransferModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={transferLoading}
                  >
                    {transferLoading ? 'Transferring...' : 'Transfer'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Edit Trading Account
              </h3>
              <form onSubmit={handleUpdateAccount}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Account Name
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    value={editData.name}
                    onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                    required
                  />
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Leverage
                  </label>
                  <select
                    className="input-field"
                    value={editData.leverage}
                    onChange={(e) => setEditData({ ...editData, leverage: parseInt(e.target.value) })}
                  >
                    <option value={1}>1:1</option>
                    <option value={5}>1:5</option>
                    <option value={10}>1:10</option>
                    <option value={20}>1:20</option>
                    <option value={50}>1:50</option>
                    <option value={100}>1:100</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    Update Account
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
