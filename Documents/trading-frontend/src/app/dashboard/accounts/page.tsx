'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useRouter } from 'next/navigation';
import { TradingApiService, WalletApiService, TradingAccount, Wallet, ApiError } from '../../../lib/api';
import Button from '../../../components/ui/Button';
import Card from '../../../components/ui/Card';

export default function AccountsPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [accounts, setAccounts] = useState<TradingAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newAccountData, setNewAccountData] = useState({
    name: '',
    leverage: 1,
    wallet_id: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const [wallets, setWallets] = useState<Wallet[]>([]);

  const router = useRouter();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Fetch data if authenticated
    if (isAuthenticated) {
      fetchAccounts();
      fetchWallets();
    }
  }, [authLoading, isAuthenticated, router]);

  const fetchWallets = async () => {
    try {
      const response = await WalletApiService.getWallets();
      if (response.data && response.data.results) {
        setWallets(response.data.results);
      }
    } catch (err) {
      console.error('Failed to load wallets:', err);
    }
  };

  const fetchAccounts = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await TradingApiService.getTradingAccounts();
      if (response.data && response.data.results) {
        setAccounts(response.data.results);
      }
    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to load trading accounts';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAccount = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    setSuccessMessage('');

    try {
      const response = await TradingApiService.createTradingAccount(newAccountData);
      if (response.data) {
        setAccounts([...accounts, response.data]);
        setSuccessMessage('Trading account created successfully!');
        setNewAccountData({ name: '', leverage: 1, wallet_id: '' });
        setIsCreateModalOpen(false);
        fetchAccounts();
      }
    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to create trading account';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAccountTypeLabel = (type: string) => {
    switch (type) {
      case 'DEMO':
        return 'Demo Account';
      case 'STANDARD':
        return 'Standard Account';
      case 'PREMIUM':
        return 'Premium Account';
      case 'VIP':
        return 'VIP Account';
      default:
        return type;
    }
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'DEMO':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'STANDARD':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'PREMIUM':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'VIP':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      default:
        return currency;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Trading Accounts</h1>
      
      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                {error}
              </h3>
              {error.includes('select a valid wallet') && (
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>Please create a wallet first before creating a trading account.</p>
                </div>
              )}
              {error.includes('Account limit reached') && (
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>To create more trading accounts, consider upgrading your account plan.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="rounded-md bg-green-50 dark:bg-green-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">{successMessage}</h3>
            </div>
          </div>
        </div>
      )}

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Your Trading Accounts</h2>
        <Button 
          variant="primary" 
          onClick={() => setIsCreateModalOpen(true)}
          leftIcon={
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          }
        >
          Create Account
        </Button>
      </div>

      {accounts.length === 0 ? (
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No trading accounts found</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">You don't have any trading accounts yet. Create one to get started.</p>
            <Button 
              variant="primary" 
              onClick={() => setIsCreateModalOpen(true)}
              leftIcon={
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
            >
              Create Trading Account
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.isArray(accounts) && accounts.map((account) => (
            <Card 
              key={account.id} 
              className="hover:shadow-lg transition-shadow duration-200"
              hoverable
              onClick={() => router.push(`/dashboard/accounts/${account.id}`)}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{account.name}</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    account.status === 'ACTIVE'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {account.status}
                  </span>
                </div>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Account Number</p>
                    <p className="text-base font-medium text-gray-900 dark:text-white">
                      {account.account_number}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Leverage</p>
                    <p className="text-base font-medium text-gray-900 dark:text-white">
                      {account.leverage}x
                    </p>
                  </div>
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Created</span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {new Date(account.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Create Account Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">Create New Trading Account</h3>
                    <div className="mt-4">
                      <form onSubmit={handleCreateAccount}>
                        <div className="mb-4">
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Account Name</label>
                          <input
                            type="text"
                            id="name"
                            className="input-field"
                            placeholder="My Trading Account"
                            value={newAccountData.name}
                            onChange={(e) => setNewAccountData({ ...newAccountData, name: e.target.value })}
                            required
                          />
                        </div>
                        <div className="mb-4">
                          <label htmlFor="leverage" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Leverage</label>
                          <input
                            type="number"
                            id="leverage"
                            className="input-field"
                            placeholder="1"
                            min="1"
                            max="1000"
                            value={newAccountData.leverage}
                            onChange={(e) => setNewAccountData({ ...newAccountData, leverage: Number(e.target.value) })}
                            required
                          />
                        </div>
                        <div className="mb-4">
                          <label htmlFor="wallet" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Wallet</label>
                          {wallets.length === 0 ? (
                            <div className="mt-1 p-3 border border-yellow-300 dark:border-yellow-600 rounded-md bg-yellow-50 dark:bg-yellow-900">
                              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                                You need to create a wallet first before creating a trading account.
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-2"
                                onClick={() => {
                                  setIsCreateModalOpen(false);
                                  router.push('/dashboard/wallets');
                                }}
                              >
                                Create Wallet
                              </Button>
                            </div>
                          ) : (
                            <select
                              id="wallet"
                              className="input-field"
                              value={newAccountData.wallet_id}
                              onChange={(e) => setNewAccountData({ ...newAccountData, wallet_id: e.target.value })}
                              required
                            >
                              <option value="">Select Wallet</option>
                              {wallets.map((wallet) => (
                                <option key={wallet.id} value={wallet.id}>
                                  {wallet.name} ({wallet.currency})
                                </option>
                              ))}
                            </select>
                          )}
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <Button
                  variant="primary"
                  onClick={handleCreateAccount}
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  className="w-full sm:w-auto sm:ml-3"
                >
                  Create Account
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateModalOpen(false)}
                  disabled={isSubmitting}
                  className="mt-3 w-full sm:mt-0 sm:w-auto"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
