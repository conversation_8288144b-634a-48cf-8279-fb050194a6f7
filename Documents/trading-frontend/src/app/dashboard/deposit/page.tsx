'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { WalletApiService, Wallet, ApiError } from '../../../lib/api';
import Button from '../../../components/ui/Button';
import Card from '../../../components/ui/Card';

export default function DepositPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [selectedWallet, setSelectedWallet] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [depositAmount, setDepositAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('CARD');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const walletParam = searchParams?.get('wallet');

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Fetch wallets if authenticated
    if (isAuthenticated) {
      fetchWallets();
    }
  }, [authLoading, isAuthenticated, router]);

  useEffect(() => {
    // Set selected wallet from URL parameter if available
    if (walletParam && wallets.length > 0) {
      const wallet = wallets.find(w => w.id === walletParam);
      if (wallet) {
        setSelectedWallet(walletParam);
      }
    }
  }, [walletParam, wallets]);

  const fetchWallets = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await WalletApiService.getWallets();
      if (response.data && response.data.results) {
        setWallets(response.data.results);

        // If no wallet is selected and we have wallets, select the first one
        if (!selectedWallet && response.data.results.length > 0 && !walletParam) {
          setSelectedWallet(response.data.results[0].id);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to load wallets';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    setSuccessMessage('');

    try {
      await WalletApiService.createDeposit({
        wallet_id: selectedWallet,
        amount: depositAmount,
        payment_method: paymentMethod as 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL',
        description: 'Wallet deposit',
      });

      setSuccessMessage(`Successfully initiated deposit of ${getSelectedWalletCurrency()} ${depositAmount} to your wallet. Please check your email for confirmation.`);
      setDepositAmount('');

      // Refresh wallets to show updated balance
      fetchWallets();

    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to process deposit';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedWallet = () => {
    return wallets.find(wallet => wallet.id === selectedWallet);
  };

  const getSelectedWalletCurrency = () => {
    const wallet = getSelectedWallet();
    return wallet ? wallet.currency : 'USD';
  };

  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      default:
        return currency;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (wallets.length === 0) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Deposit Funds</h1>
        
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No wallets found</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">You need to create a wallet before you can deposit funds.</p>
            <Button 
              variant="primary" 
              onClick={() => router.push('/dashboard/wallets')}
              leftIcon={
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
            >
              Create Wallet
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Deposit Funds</h1>
      
      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">{error}</h3>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="rounded-md bg-green-50 dark:bg-green-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">{successMessage}</h3>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Deposit to Your Wallet</h2>
              
              <form onSubmit={handleDeposit}>
                <div className="mb-6">
                  <label htmlFor="wallet" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Wallet
                  </label>
                  <select
                    id="wallet"
                    className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                    value={selectedWallet}
                    onChange={(e) => setSelectedWallet(e.target.value)}
                    required
                  >
                    {wallets.map((wallet) => (
                      <option key={wallet.id} value={wallet.id}>
                        {wallet.name} ({wallet.currency})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="mb-6">
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Amount
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                        {getCurrencyIcon(getSelectedWalletCurrency())}
                      </span>
                    </div>
                    <input
                      type="number"
                      id="amount"
                      className="pl-7 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                      placeholder="0.00"
                      step="0.01"
                      min="10"
                      value={depositAmount}
                      onChange={(e) => setDepositAmount(e.target.value)}
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Minimum deposit: {getCurrencyIcon(getSelectedWalletCurrency())} 10.00
                  </p>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Payment Method
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'CARD'
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('CARD')}
                    >
                      <div className="flex items-center">
                        <input
                          id="card"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'CARD'}
                          onChange={() => setPaymentMethod('CARD')}
                        />
                        <label htmlFor="card" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Credit / Debit Card
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Instant deposit with Visa, Mastercard, or American Express
                      </p>
                    </div>
                    
                    <div
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'BANK_TRANSFER'
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('BANK_TRANSFER')}
                    >
                      <div className="flex items-center">
                        <input
                          id="bank_transfer"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'BANK_TRANSFER'}
                          onChange={() => setPaymentMethod('BANK_TRANSFER')}
                        />
                        <label htmlFor="bank_transfer" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Bank Transfer
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        1-3 business days to process
                      </p>
                    </div>
                    
                    <div
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'CRYPTO'
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('CRYPTO')}
                    >
                      <div className="flex items-center">
                        <input
                          id="crypto"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'CRYPTO'}
                          onChange={() => setPaymentMethod('CRYPTO')}
                        />
                        <label htmlFor="crypto" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cryptocurrency
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Deposit using Bitcoin, Ethereum, or other cryptocurrencies
                      </p>
                    </div>
                    
                    <div
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'PAYPAL'
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('PAYPAL')}
                    >
                      <div className="flex items-center">
                        <input
                          id="paypal"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'PAYPAL'}
                          onChange={() => setPaymentMethod('PAYPAL')}
                        />
                        <label htmlFor="paypal" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          PayPal
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        PayPal, Skrill, or Neteller
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <Button
                    type="submit"
                    variant="primary"
                    className="w-full"
                    isLoading={isSubmitting}
                    disabled={isSubmitting || !selectedWallet || !depositAmount || parseFloat(depositAmount) < 10}
                  >
                    Deposit Funds
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Deposit Information</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Processing Time</h4>
                  <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Credit/Debit Card: Instant</li>
                    <li>• Bank Transfer: 1-3 business days</li>
                    <li>• Cryptocurrency: 10-60 minutes</li>
                    <li>• E-Wallet: Instant to 24 hours</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Fees</h4>
                  <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Credit/Debit Card: 2.5%</li>
                    <li>• Bank Transfer: Free</li>
                    <li>• Cryptocurrency: Network fee</li>
                    <li>• E-Wallet: 1-3% depending on provider</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Limits</h4>
                  <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Minimum: {getCurrencyIcon(getSelectedWalletCurrency())} 10.00</li>
                    <li>• Maximum: Depends on verification level</li>
                  </ul>
                </div>
                
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Need Help?</h4>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Contact our support team for assistance with deposits.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => router.push('/dashboard/support')}
                  >
                    Contact Support
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
