'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useRouter } from 'next/navigation';
import {
  WalletApiService,
  TradingApiService,
  Wallet,
  TradingAccount,
  Trade,
  ApiError
} from '../../lib/api';
import SummaryCard from '../../components/cards/SummaryCard';
import WalletCard from '../../components/cards/WalletCard';
import AccountCard from '../../components/cards/AccountCard';
import TradeCard from '../../components/cards/TradeCard';
import ActivityCard from '../../components/cards/ActivityCard';

export default function Dashboard() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [accounts, setAccounts] = useState<TradingAccount[]>([]);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [activities, setActivities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const router = useRouter();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Fetch data if authenticated
    if (isAuthenticated) {
      const fetchData = async () => {
        setIsLoading(true);
        setError('');

        try {
          const [walletsResponse, accountsResponse, tradesResponse] = await Promise.all([
            WalletApiService.getWallets(),
            TradingApiService.getTradingAccounts(),
            TradingApiService.getTrades()
          ]);

          if (walletsResponse.data && walletsResponse.data.results) {
            setWallets(walletsResponse.data.results);
          }

          if (accountsResponse.data && accountsResponse.data.results) {
            setAccounts(accountsResponse.data.results);
          }

          if (tradesResponse.data && tradesResponse.data.results) {
            setTrades(tradesResponse.data.results);
          }

          // For now, we'll skip activities since we don't have that API endpoint yet
          setActivities([]);

        } catch (err) {
          const errorMessage = err instanceof ApiError
            ? err.message
            : 'Failed to load dashboard data';
          setError(errorMessage);
        } finally {
          setIsLoading(false);
        }
      };

      fetchData();
    }
  }, [authLoading, isAuthenticated, router]);

  // Total balance is calculated in the UI directly

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Loading...</h2>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Please wait while we load your dashboard</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Dashboard</h1>

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">{error}</h3>
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <SummaryCard
          title="Total Wallets"
          value={wallets.length}
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          }
          onClick={() => router.push('/dashboard/wallets')}
        />
        <SummaryCard
          title="Trading Accounts"
          value={Array.isArray(accounts) ? accounts.length : 0}
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
          onClick={() => router.push('/dashboard/accounts')}
        />
        <SummaryCard
          title="Open Trades"
          value={Array.isArray(trades) ? trades.filter((trade: Trade) => trade.status === 'OPEN').length : 0}
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          }
          onClick={() => router.push('/dashboard/trades')}
        />
        <SummaryCard
          title="User Account"
          value={user?.email || 'User'}
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          onClick={() => router.push('/dashboard/profile')}
        />
      </div>

      {/* Main Dashboard Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <WalletCard wallets={Array.isArray(wallets) ? wallets.slice(0, 3) : []} />
          <AccountCard accounts={Array.isArray(accounts) ? accounts.slice(0, 3) : []} />
        </div>
        <div className="space-y-6">
          <ActivityCard activities={Array.isArray(activities) ? activities : []} />
          <TradeCard trades={Array.isArray(trades) ? trades.slice(0, 3) : []} />
        </div>
      </div>
    </div>
  );
}
