'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { getTradingAccounts } from '../../../api/trading';
import Button from '../../../components/ui/Button';
import Card from '../../../components/ui/Card';

export default function TradingPage() {
  const { token } = useAuth();
  const [accounts, setAccounts] = useState<any[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const accountParam = searchParams?.get('account');

  useEffect(() => {
    // Fetch accounts if authenticated
    if (token) {
      fetchAccounts();
    }
  }, [token]);

  useEffect(() => {
    // Set selected account from URL parameter if available
    if (accountParam && accounts.length > 0) {
      const account = accounts.find(a => a.id === accountParam);
      if (account) {
        setSelectedAccount(accountParam);
      }
    }
  }, [accountParam, accounts]);

  const fetchAccounts = async () => {
    setIsLoading(true);
    setError('');

    try {
      const accountsData = await getTradingAccounts(token!);
      setAccounts(Array.isArray(accountsData) ? accountsData : []);
      
      // If no account is selected and we have accounts, select the first one
      if (!selectedAccount && Array.isArray(accountsData) && accountsData.length > 0 && !accountParam) {
        setSelectedAccount(accountsData[0].id);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load trading accounts');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Trading Platform</h1>
      
      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">{error}</h3>
            </div>
          </div>
        </div>
      )}

      {accounts.length === 0 ? (
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No trading accounts found</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">You need to create a trading account before you can start trading.</p>
            <Button 
              variant="primary" 
              onClick={() => router.push('/dashboard/accounts')}
              leftIcon={
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
            >
              Create Trading Account
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          <Card className="p-6">
            <div className="mb-6">
              <label htmlFor="account" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Trading Account
              </label>
              <select
                id="account"
                className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                value={selectedAccount}
                onChange={(e) => setSelectedAccount(e.target.value)}
              >
                {accounts.map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.name} ({account.currency}) - Balance: {account.balance}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="text-center py-12">
              <svg className="h-24 w-24 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Trading Platform Coming Soon</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                We're working on integrating a full-featured trading platform. Check back soon!
              </p>
              <div className="flex justify-center space-x-4">
                <Button 
                  variant="outline" 
                  onClick={() => router.push(`/dashboard/accounts/${selectedAccount}`)}
                >
                  View Account Details
                </Button>
                <Button 
                  variant="primary" 
                  onClick={() => router.push('/dashboard/trades')}
                >
                  View Trading History
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
