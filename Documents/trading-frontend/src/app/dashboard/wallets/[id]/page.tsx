'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { WalletApiService } from '../../../../lib/api/wallet';
import { TradingApiService } from '../../../../lib/api/trading';
import { Wallet, TradingAccount, Transaction } from '../../../../types/api';
import Button from '../../../../components/ui/Button';
import { ArrowLeftIcon, ArrowUpIcon, ArrowDownIcon, CreditCardIcon, BanknotesIcon } from '@heroicons/react/24/outline';

export default function WalletDetailPage() {
  const params = useParams();
  const router = useRouter();
  const walletId = params.id as string;

  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [tradingAccounts, setTradingAccounts] = useState<TradingAccount[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Transfer modal states
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false);
  const [transferData, setTransferData] = useState({
    trading_account_id: '',
    amount: '',
    description: '',
  });
  const [transferLoading, setTransferLoading] = useState(false);

  // Edit modal states
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editData, setEditData] = useState({
    name: '',
  });

  useEffect(() => {
    fetchWalletData();
  }, [walletId]);

  const fetchWalletData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ensure walletId is a string
      const walletIdString = String(walletId);

      // Fetch wallet details, trading accounts, and transactions in parallel
      const [walletResponse, accountsResponse, transactionsResponse] = await Promise.all([
        WalletApiService.getWallet(walletIdString),
        TradingApiService.getTradingAccounts(),
        WalletApiService.getTransactions({ wallet_id: walletIdString }),
      ]);

      if (walletResponse.data) {
        setWallet(walletResponse.data as any);
        setEditData({ name: walletResponse.data.name });
      }

      if (accountsResponse.data && accountsResponse.data.results) {
        // Filter trading accounts linked to this wallet
        // Check both wallet ID and wallet object reference
        const linkedAccounts = accountsResponse.data.results.filter((account: any) => {
          const accountWalletId = typeof account.wallet === 'object' ? account.wallet.id : account.wallet;
          return accountWalletId === walletIdString;
        });
        setTradingAccounts(linkedAccounts as any);
        console.log('Linked accounts found:', linkedAccounts.length, linkedAccounts);
      }

      if (transactionsResponse.data && transactionsResponse.data.results) {
        setTransactions(transactionsResponse.data.results);
      }
    } catch (err: any) {
      console.error('Wallet data fetch error:', err);
      setError(err.message || 'Failed to load wallet data');
    } finally {
      setLoading(false);
    }
  };

  const handleTransferToAccount = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!transferData.trading_account_id || !transferData.amount) return;

    try {
      setTransferLoading(true);
      const walletIdString = String(walletId);
      await WalletApiService.transferToAccount(walletIdString, {
        trading_account_id: transferData.trading_account_id,
        amount: transferData.amount,
        description: transferData.description || 'Transfer to trading account',
      });

      setIsTransferModalOpen(false);
      setTransferData({ trading_account_id: '', amount: '', description: '' });
      await fetchWalletData(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Transfer failed');
    } finally {
      setTransferLoading(false);
    }
  };

  const handleUpdateWallet = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editData.name.trim()) return;

    try {
      const walletIdString = String(walletId);
      await WalletApiService.updateWallet(walletIdString, editData);
      setIsEditModalOpen(false);
      await fetchWalletData(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Update failed');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!wallet) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Wallet Not Found</h2>
          <Button onClick={() => router.push('/dashboard/wallets')}>
            Back to Wallets
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dashboard/wallets')}
                className="mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Wallets
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {wallet.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {wallet.currency} Wallet • Created {new Date(wallet.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setIsEditModalOpen(true)}
              >
                Edit Wallet
              </Button>
              <Button
                onClick={() => setIsTransferModalOpen(true)}
                disabled={tradingAccounts.length === 0}
              >
                <ArrowUpIcon className="h-4 w-4 mr-2" />
                Transfer to Account
              </Button>
            </div>
          </div>
        </div>

        {error && (
          <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  {error}
                </h3>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Wallet Overview */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Wallet Overview
              </h2>

              <div className="space-y-4">
                <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-600 dark:text-blue-400">Available Balance</p>
                      <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        {parseFloat(wallet.balance).toLocaleString()} {wallet.currency}
                      </p>
                    </div>
                    <BanknotesIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">Status</span>
                    <span className={`font-medium ${wallet.is_active ? 'text-green-600' : 'text-red-600'}`}>
                      {wallet.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-gray-500 dark:text-gray-400">Currency</span>
                    <span className="font-medium text-gray-900 dark:text-white">{wallet.currency}</span>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-gray-500 dark:text-gray-400">Linked Accounts</span>
                    <span className="font-medium text-gray-900 dark:text-white">{tradingAccounts.length}</span>
                  </div>
                </div>

                <div className="pt-4 space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push('/dashboard/deposit')}
                  >
                    <ArrowDownIcon className="h-4 w-4 mr-2" />
                    Deposit Funds
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push('/dashboard/withdraw')}
                  >
                    <ArrowUpIcon className="h-4 w-4 mr-2" />
                    Withdraw Funds
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Linked Trading Accounts & Recent Transactions */}
          <div className="lg:col-span-2 space-y-8">
            {/* Linked Trading Accounts */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Linked Trading Accounts ({tradingAccounts.length})
              </h2>

              {tradingAccounts.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No trading accounts</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Create a trading account to start trading with this wallet.
                  </p>
                  <div className="mt-6">
                    <Button onClick={() => router.push('/dashboard/accounts')}>
                      Create Trading Account
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {tradingAccounts.map((account) => (
                    <div
                      key={account.id}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                      onClick={() => router.push(`/dashboard/accounts/${account.id}`)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                            {account.name}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {account.account_number} • Leverage: {account.leverage}x
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {parseFloat(account.balance).toLocaleString()} {wallet.currency}
                          </p>
                          <p className={`text-xs ${account.status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'}`}>
                            {account.status}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Recent Transactions */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Recent Transactions
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/dashboard/activities')}
                >
                  View All
                </Button>
              </div>

              {transactions.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-gray-400 text-sm">No transactions yet</div>
                </div>
              ) : (
                <div className="space-y-3">
                  {transactions.slice(0, 5).map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                    >
                      <div className="flex items-center">
                        <div className={`p-2 rounded-full mr-3 ${
                          transaction.transaction_type === 'DEPOSIT'
                            ? 'bg-green-100 dark:bg-green-900'
                            : 'bg-red-100 dark:bg-red-900'
                        }`}>
                          {transaction.transaction_type === 'DEPOSIT' ? (
                            <ArrowDownIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                          ) : (
                            <ArrowUpIcon className="h-4 w-4 text-red-600 dark:text-red-400" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {transaction.transaction_type}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {transaction.payment_method} • {new Date(transaction.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${
                          transaction.transaction_type === 'DEPOSIT'
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-red-600 dark:text-red-400'
                        }`}>
                          {transaction.transaction_type === 'DEPOSIT' ? '+' : '-'}
                          {parseFloat(transaction.amount).toLocaleString()} {wallet.currency}
                        </p>
                        <p className={`text-xs ${
                          transaction.status === 'COMPLETED' ? 'text-green-600' :
                          transaction.status === 'PENDING' ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {transaction.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Transfer Modal */}
      {isTransferModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Transfer to Trading Account
              </h3>
              <form onSubmit={handleTransferToAccount}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Trading Account
                  </label>
                  <select
                    className="input-field"
                    value={transferData.trading_account_id}
                    onChange={(e) => setTransferData({ ...transferData, trading_account_id: e.target.value })}
                    required
                  >
                    <option value="">Select Account</option>
                    {tradingAccounts.map((account) => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({parseFloat(account.balance).toLocaleString()} {wallet.currency})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Amount ({wallet.currency})
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    max={wallet.balance}
                    className="input-field"
                    value={transferData.amount}
                    onChange={(e) => setTransferData({ ...transferData, amount: e.target.value })}
                    required
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Available: {parseFloat(wallet.balance).toLocaleString()} {wallet.currency}
                  </p>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description (Optional)
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    value={transferData.description}
                    onChange={(e) => setTransferData({ ...transferData, description: e.target.value })}
                    placeholder="Transfer description"
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsTransferModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={transferLoading}
                  >
                    {transferLoading ? 'Transferring...' : 'Transfer'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Edit Wallet
              </h3>
              <form onSubmit={handleUpdateWallet}>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Wallet Name
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    value={editData.name}
                    onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                    required
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    Update Wallet
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
