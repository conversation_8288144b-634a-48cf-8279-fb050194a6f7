'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useRouter } from 'next/navigation';
import { WalletApiService, Wallet, ApiError } from '../../../lib/api';
import Button from '../../../components/ui/Button';
import Card from '../../../components/ui/Card';

export default function WalletsPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newWalletData, setNewWalletData] = useState({
    name: '',
    currency: 'USD',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const router = useRouter();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Fetch wallets if authenticated
    if (isAuthenticated) {
      fetchWallets();
    }
  }, [authLoading, isAuthenticated, router]);

  const fetchWallets = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await WalletApiService.getWallets();
      if (response.data && response.data.results) {
        setWallets(response.data.results);
      }
    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to load wallets';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateWallet = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    setSuccessMessage('');

    try {
      const response = await WalletApiService.createWallet(newWalletData);
      if (response.data) {
        setWallets([...wallets, response.data]);
        setSuccessMessage('Wallet created successfully!');
        setNewWalletData({ name: '', currency: 'USD' });
        setIsCreateModalOpen(false);

        // Refresh wallets list
        fetchWallets();
      }
    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to create wallet';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      default:
        return currency;
    }
  };

  const getCurrencyColor = (currency: string) => {
    switch (currency) {
      case 'USD':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'EUR':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'GBP':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'JPY':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'BTC':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'ETH':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Wallet Management</h1>
      
      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                {error}
              </h3>
              {error.includes('maximum wallet limit') && (
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>To create more wallets, consider upgrading your account:</p>
                  <ul className="list-disc list-inside mt-1">
                    <li>Basic: 1 wallet</li>
                    <li>Premium: 3 wallets</li>
                    <li>VIP: Unlimited wallets</li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="rounded-md bg-green-50 dark:bg-green-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">{successMessage}</h3>
            </div>
          </div>
        </div>
      )}

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Your Wallets</h2>
        <Button 
          variant="primary" 
          onClick={() => setIsCreateModalOpen(true)}
          leftIcon={
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          }
        >
          Create Wallet
        </Button>
      </div>

      {wallets.length === 0 ? (
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No wallets found</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">You don't have any wallets yet. Create one to get started.</p>
            <Button 
              variant="primary" 
              onClick={() => setIsCreateModalOpen(true)}
              leftIcon={
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
            >
              Create Wallet
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.isArray(wallets) && wallets.map((wallet) => (
            <Card 
              key={wallet.id} 
              className="hover:shadow-lg transition-shadow duration-200"
              hoverable
              onClick={() => router.push(`/dashboard/wallets/${wallet.id}`)}
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className={`flex-shrink-0 h-12 w-12 rounded-full ${getCurrencyColor(wallet.currency)} flex items-center justify-center text-xl font-bold`}>
                    {getCurrencyIcon(wallet.currency)}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{wallet.name}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{wallet.currency}</p>
                  </div>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Balance</span>
                    <span className="text-xl font-bold text-gray-900 dark:text-white">
                      {getCurrencyIcon(wallet.currency)} {parseFloat(wallet.balance).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 })}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Create Wallet Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">Create New Wallet</h3>
                    <div className="mt-4">
                      <form onSubmit={handleCreateWallet}>
                        <div className="mb-4">
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Wallet Name</label>
                          <input
                            type="text"
                            id="name"
                            className="input-field"
                            placeholder="My USD Wallet"
                            value={newWalletData.name}
                            onChange={(e) => setNewWalletData({ ...newWalletData, name: e.target.value })}
                            required
                          />
                        </div>
                        <div className="mb-4">
                          <label htmlFor="currency" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Currency</label>
                          <select
                            id="currency"
                            className="input-field"
                            value={newWalletData.currency}
                            onChange={(e) => setNewWalletData({ ...newWalletData, currency: e.target.value })}
                            required
                          >
                            <option value="USD">US Dollar (USD)</option>
                            <option value="EUR">Euro (EUR)</option>
                            <option value="GBP">British Pound (GBP)</option>
                            <option value="JPY">Japanese Yen (JPY)</option>
                            <option value="BTC">Bitcoin (BTC)</option>
                            <option value="ETH">Ethereum (ETH)</option>
                          </select>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <Button
                  variant="primary"
                  onClick={handleCreateWallet}
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  className="w-full sm:w-auto sm:ml-3"
                >
                  Create Wallet
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateModalOpen(false)}
                  disabled={isSubmitting}
                  className="mt-3 w-full sm:mt-0 sm:w-auto"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
