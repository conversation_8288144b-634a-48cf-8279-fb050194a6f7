'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { WalletApiService, Wallet, ApiError } from '../../../lib/api';
import Button from '../../../components/ui/Button';
import Card from '../../../components/ui/Card';

export default function WithdrawPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [selectedWallet, setSelectedWallet] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('BANK_TRANSFER');
  const [withdrawalDetails, setWithdrawalDetails] = useState({
    bank_name: '',
    account_number: '',
    routing_number: '',
    account_holder: '',
    crypto_address: '',
    e_wallet_email: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const walletParam = searchParams?.get('wallet');

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Fetch wallets if authenticated
    if (isAuthenticated) {
      fetchWallets();
    }
  }, [authLoading, isAuthenticated, router]);

  useEffect(() => {
    // Set selected wallet from URL parameter if available
    if (walletParam && wallets.length > 0) {
      const wallet = wallets.find(w => w.id === walletParam);
      if (wallet) {
        setSelectedWallet(walletParam);
      }
    }
  }, [walletParam, wallets]);

  const fetchWallets = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await WalletApiService.getWallets();
      if (response.data && response.data.results) {
        setWallets(response.data.results);

        // If no wallet is selected and we have wallets, select the first one
        if (!selectedWallet && response.data.results.length > 0 && !walletParam) {
          setSelectedWallet(response.data.results[0].id);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to load wallets';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleWithdraw = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    setSuccessMessage('');

    try {
      // Validate withdrawal amount against wallet balance
      const wallet = getSelectedWallet();
      if (wallet && parseFloat(withdrawAmount) > parseFloat(wallet.balance)) {
        throw new Error('Withdrawal amount exceeds wallet balance');
      }

      // Call the real API for withdrawal
      await WalletApiService.createWithdrawal({
        wallet_id: selectedWallet,
        amount: withdrawAmount,
        payment_method: paymentMethod as 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL',
        description: 'Wallet withdrawal',
      });
      
      setSuccessMessage(`Successfully initiated withdrawal of ${getSelectedWalletCurrency()} ${withdrawAmount} from your wallet. Please check your email for confirmation.`);
      setWithdrawAmount('');

      // Refresh wallets to show updated balance
      fetchWallets();

    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to process withdrawal';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedWallet = () => {
    return wallets.find(wallet => wallet.id === selectedWallet);
  };

  const getSelectedWalletCurrency = () => {
    const wallet = getSelectedWallet();
    return wallet ? wallet.currency : 'USD';
  };

  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      default:
        return currency;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (wallets.length === 0) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Withdraw Funds</h1>
        
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No wallets found</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">You need to create a wallet before you can withdraw funds.</p>
            <Button 
              variant="primary" 
              onClick={() => router.push('/dashboard/wallets')}
              leftIcon={
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
            >
              Create Wallet
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Withdraw Funds</h1>
      
      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">{error}</h3>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="rounded-md bg-green-50 dark:bg-green-900 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">{successMessage}</h3>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Withdraw from Your Wallet</h2>
              
              <form onSubmit={handleWithdraw}>
                <div className="mb-6">
                  <label htmlFor="wallet" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Wallet
                  </label>
                  <select
                    id="wallet"
                    className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                    value={selectedWallet}
                    onChange={(e) => setSelectedWallet(e.target.value)}
                    required
                  >
                    {wallets.map((wallet) => (
                      <option key={wallet.id} value={wallet.id}>
                        {wallet.name} ({wallet.currency}) - Balance: {getCurrencyIcon(wallet.currency)} {parseFloat(wallet.balance).toLocaleString()}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="mb-6">
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Amount
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                        {getCurrencyIcon(getSelectedWalletCurrency())}
                      </span>
                    </div>
                    <input
                      type="number"
                      id="amount"
                      className="pl-7 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                      placeholder="0.00"
                      step="0.01"
                      min="10"
                      max={getSelectedWallet()?.balance}
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Available balance: {getCurrencyIcon(getSelectedWalletCurrency())} {parseFloat(getSelectedWallet()?.balance || '0').toLocaleString()}
                  </p>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Withdrawal Method
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div 
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'bank_transfer' 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('bank_transfer')}
                    >
                      <div className="flex items-center">
                        <input
                          id="bank_transfer"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'bank_transfer'}
                          onChange={() => setPaymentMethod('bank_transfer')}
                        />
                        <label htmlFor="bank_transfer" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Bank Transfer
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        1-3 business days to process
                      </p>
                    </div>
                    
                    <div 
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'crypto' 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('crypto')}
                    >
                      <div className="flex items-center">
                        <input
                          id="crypto"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'crypto'}
                          onChange={() => setPaymentMethod('crypto')}
                        />
                        <label htmlFor="crypto" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cryptocurrency
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Withdraw to your crypto wallet
                      </p>
                    </div>
                    
                    <div 
                      className={`border rounded-md p-4 cursor-pointer ${
                        paymentMethod === 'e_wallet' 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                          : 'border-gray-300 dark:border-gray-700'
                      }`}
                      onClick={() => setPaymentMethod('e_wallet')}
                    >
                      <div className="flex items-center">
                        <input
                          id="e_wallet"
                          name="payment_method"
                          type="radio"
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          checked={paymentMethod === 'e_wallet'}
                          onChange={() => setPaymentMethod('e_wallet')}
                        />
                        <label htmlFor="e_wallet" className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                          E-Wallet
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        PayPal, Skrill, or Neteller
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Conditional fields based on payment method */}
                {paymentMethod === 'bank_transfer' && (
                  <div className="mb-6 space-y-4">
                    <div>
                      <label htmlFor="bank_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Bank Name
                      </label>
                      <input
                        type="text"
                        id="bank_name"
                        className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        value={withdrawalDetails.bank_name}
                        onChange={(e) => setWithdrawalDetails({...withdrawalDetails, bank_name: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="account_number" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Account Number
                      </label>
                      <input
                        type="text"
                        id="account_number"
                        className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        value={withdrawalDetails.account_number}
                        onChange={(e) => setWithdrawalDetails({...withdrawalDetails, account_number: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="routing_number" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Routing Number
                      </label>
                      <input
                        type="text"
                        id="routing_number"
                        className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        value={withdrawalDetails.routing_number}
                        onChange={(e) => setWithdrawalDetails({...withdrawalDetails, routing_number: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="account_holder" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Account Holder Name
                      </label>
                      <input
                        type="text"
                        id="account_holder"
                        className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        value={withdrawalDetails.account_holder}
                        onChange={(e) => setWithdrawalDetails({...withdrawalDetails, account_holder: e.target.value})}
                        required
                      />
                    </div>
                  </div>
                )}
                
                {paymentMethod === 'crypto' && (
                  <div className="mb-6">
                    <label htmlFor="crypto_address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Crypto Address
                    </label>
                    <input
                      type="text"
                      id="crypto_address"
                      className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                      placeholder="Enter your wallet address"
                      value={withdrawalDetails.crypto_address}
                      onChange={(e) => setWithdrawalDetails({...withdrawalDetails, crypto_address: e.target.value})}
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Please double-check your address. We are not responsible for funds sent to incorrect addresses.
                    </p>
                  </div>
                )}
                
                {paymentMethod === 'e_wallet' && (
                  <div className="mb-6">
                    <label htmlFor="e_wallet_email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      E-Wallet Email
                    </label>
                    <input
                      type="email"
                      id="e_wallet_email"
                      className="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                      placeholder="<EMAIL>"
                      value={withdrawalDetails.e_wallet_email}
                      onChange={(e) => setWithdrawalDetails({...withdrawalDetails, e_wallet_email: e.target.value})}
                      required
                    />
                  </div>
                )}
                
                <div className="mt-8">
                  <Button
                    type="submit"
                    variant="primary"
                    className="w-full"
                    isLoading={isSubmitting}
                    disabled={
                      isSubmitting || 
                      !selectedWallet || 
                      !withdrawAmount || 
                      parseFloat(withdrawAmount) < 10 ||
                      (getSelectedWallet() && parseFloat(withdrawAmount) > parseFloat(getSelectedWallet()!.balance))
                    }
                  >
                    Withdraw Funds
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Withdrawal Information</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Processing Time</h4>
                  <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Bank Transfer: 1-3 business days</li>
                    <li>• Cryptocurrency: 10-60 minutes</li>
                    <li>• E-Wallet: 24-48 hours</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Fees</h4>
                  <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Bank Transfer: $15 flat fee</li>
                    <li>• Cryptocurrency: Network fee</li>
                    <li>• E-Wallet: 1-3% depending on provider</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Limits</h4>
                  <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Minimum: {getCurrencyIcon(getSelectedWalletCurrency())} 10.00</li>
                    <li>• Maximum: Depends on verification level</li>
                  </ul>
                </div>
                
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Important Notice</h4>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Withdrawals are processed during business hours. Requests made after hours or on weekends will be processed on the next business day.
                  </p>
                </div>
                
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Need Help?</h4>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Contact our support team for assistance with withdrawals.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => router.push('/dashboard/support')}
                  >
                    Contact Support
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
