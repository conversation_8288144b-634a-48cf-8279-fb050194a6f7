'use client';

import { useEffect, useState } from 'react';
import { useStoreInitialization } from '../../stores';

export default function TestPage() {
  const [renderCount, setRenderCount] = useState(0);

  // Initialize stores - commented out for testing
  // useStoreInitialization();

  useEffect(() => {
    setRenderCount(prev => prev + 1);
    console.log('TestPage rendered:', renderCount + 1);
  });

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Store Initialization Test</h1>
      <p className="text-lg">Render count: {renderCount}</p>
      <p className="text-sm text-gray-600 mt-2">
        If this number keeps increasing rapidly, there's still an infinite loop.
      </p>
      <p className="text-sm text-gray-600">
        Check the browser console for render logs.
      </p>
    </div>
  );
}
