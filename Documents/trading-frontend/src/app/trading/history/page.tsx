'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { useTheme } from '../../../context/ThemeContext';
import { useRouter } from 'next/navigation';
import { getTrades } from '../../../api/trading';
import Button from '../../../components/ui/Button';

export default function HistoryPage() {
  const { token, isAuthenticated, isLoading: authLoading } = useAuth();
  const [trades, setTrades] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('ALL');

  const router = useRouter();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Load trade history
    if (isAuthenticated && token) {
      const fetchTrades = async () => {
        setIsLoading(true);
        setError('');

        try {
          // Get closed trades
          const tradesData = await getTrades(token, undefined, 'CLOSED');
          setTrades(Array.isArray(tradesData) ? tradesData : []);
        } catch (err: any) {
          setError(err.message || 'Failed to load trade history');
        } finally {
          setIsLoading(false);
        }
      };

      fetchTrades();
    }
  }, [authLoading, isAuthenticated, token, router]);

  // Filter trades based on selected filter
  const filteredTrades = trades.filter(trade => {
    if (filter === 'ALL') return true;
    if (filter === 'PROFIT') return parseFloat(trade.profit_loss) > 0;
    if (filter === 'LOSS') return parseFloat(trade.profit_loss) < 0;
    return true;
  });

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-400">Loading trade history...</p>
        </div>
      </div>
    );
  }

  const { theme } = useTheme();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Trade History</h1>
        <div className="flex flex-wrap gap-2">
          <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'} rounded-lg p-1 flex`}>
            <button
              className={`px-3 py-1 text-xs font-medium rounded ${
                filter === 'ALL'
                  ? theme === 'dark'
                    ? 'bg-gray-700 text-white'
                    : 'bg-white text-gray-900'
                  : theme === 'dark'
                    ? 'text-gray-400 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setFilter('ALL')}
            >
              All
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium rounded ${
                filter === 'PROFIT'
                  ? theme === 'dark'
                    ? 'bg-green-800 text-white'
                    : 'bg-green-600 text-white'
                  : theme === 'dark'
                    ? 'text-gray-400 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setFilter('PROFIT')}
            >
              Profit
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium rounded ${
                filter === 'LOSS'
                  ? theme === 'dark'
                    ? 'bg-red-800 text-white'
                    : 'bg-red-600 text-white'
                  : theme === 'dark'
                    ? 'text-gray-400 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setFilter('LOSS')}
            >
              Loss
            </button>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/trading')}
          >
            New Trade
          </Button>
        </div>
      </div>

      {error && (
        <div className={`${theme === 'dark' ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'} p-4 rounded-md mb-6`}>
          <p>{error}</p>
        </div>
      )}

      {trades.length === 0 ? (
        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white border border-gray-200'} rounded-lg p-8 text-center shadow-sm`}>
          <svg className={`h-16 w-16 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className={`text-xl font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-2`}>No Trade History</h3>
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mb-6`}>
            You haven't completed any trades yet.
          </p>
          <Button
            variant="primary"
            onClick={() => router.push('/trading')}
          >
            Start Trading
          </Button>
        </div>
      ) : filteredTrades.length === 0 ? (
        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white border border-gray-200'} rounded-lg p-8 text-center shadow-sm`}>
          <h3 className={`text-xl font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-2`}>No Matching Trades</h3>
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mb-6`}>
            No trades match the selected filter.
          </p>
          <Button
            variant="outline"
            onClick={() => setFilter('ALL')}
          >
            Show All Trades
          </Button>
        </div>
      ) : (
        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white border border-gray-200'} rounded-lg overflow-hidden shadow-sm`}>
          <div className="overflow-x-auto">
            <table className={`min-w-full divide-y ${theme === 'dark' ? 'divide-gray-700' : 'divide-gray-200'}`}>
              <thead className={theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}>
                <tr>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    Asset
                  </th>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    Type
                  </th>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    Amount
                  </th>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    Entry Price
                  </th>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    Close Price
                  </th>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    P/L
                  </th>
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className={`${theme === 'dark' ? 'bg-gray-800 divide-y divide-gray-700' : 'bg-white divide-y divide-gray-200'}`}>
                {filteredTrades.map((trade) => {
                  const isProfit = parseFloat(trade.profit_loss) > 0;
                  const closeDate = new Date(trade.close_time);

                  return (
                    <tr key={trade.id} className={theme === 'dark' ? '' : 'hover:bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="ml-4">
                            <div className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                              {trade.asset.symbol}
                            </div>
                            <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                              {trade.asset.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          trade.trade_type === 'BUY'
                            ? theme === 'dark' ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                            : theme === 'dark' ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                        }`}>
                          {trade.trade_type === 'BUY' ? 'Long' : 'Short'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                          ${parseFloat(trade.volume).toLocaleString()}
                        </div>
                        <div className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          {trade.leverage}x leverage
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        ${parseFloat(trade.open_price).toLocaleString()}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        ${parseFloat(trade.close_price).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${isProfit ? 'text-green-500' : 'text-red-500'}`}>
                          {isProfit ? '+' : ''}{parseFloat(trade.profit_loss).toLocaleString()}
                        </div>
                        <div className={`text-xs ${isProfit ? 'text-green-500' : 'text-red-500'}`}>
                          {isProfit ? '+' : ''}
                          {((parseFloat(trade.profit_loss) / parseFloat(trade.volume)) * 100).toFixed(2)}%
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        {closeDate.toLocaleDateString()} {closeDate.toLocaleTimeString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
