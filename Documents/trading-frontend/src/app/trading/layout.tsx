'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import Button from '../../components/ui/Button';

interface TradingLayoutProps {
  children: ReactNode;
}

export default function TradingLayout({ children }: TradingLayoutProps) {
  const { user, isAuthenticated, isLoading: authLoading, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authLoading, isAuthenticated, router]);

  const handleLogout = async () => {
    await logout();
    router.push('/auth/login');
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white">Loading...</h2>
          <p className="mt-2 text-gray-400">Please wait while we load the trading platform</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Top navigation bar */}
      <header className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b`}>
        <div className="px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex justify-between items-center">
            {/* Logo and navigation */}
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center mr-8">
                <span className="text-xl font-bold text-blue-500">Trade</span>
                <span className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Platform</span>
              </Link>

              <nav className="hidden md:flex space-x-6">
                <Link
                  href="/trading"
                  className={`text-sm font-medium ${
                    pathname === '/trading'
                      ? 'text-blue-600 dark:text-blue-400'
                      : `${theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
                  }`}
                >
                  Market
                </Link>
                <Link
                  href="/trading/positions"
                  className={`text-sm font-medium ${
                    pathname === '/trading/positions'
                      ? 'text-blue-600 dark:text-blue-400'
                      : `${theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
                  }`}
                >
                  Positions
                </Link>
                <Link
                  href="/trading/history"
                  className={`text-sm font-medium ${
                    pathname === '/trading/history'
                      ? 'text-blue-600 dark:text-blue-400'
                      : `${theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
                  }`}
                >
                  History
                </Link>
              </nav>
            </div>

            {/* User controls */}
            <div className="flex items-center space-x-4">
              <Button
                href="/dashboard"
                variant="outline"
                size="sm"
                className={`${theme === 'dark'
                  ? 'text-gray-300 border-gray-600 hover:bg-gray-700'
                  : 'text-gray-700 border-gray-300 hover:bg-gray-100'
                }`}
                leftIcon={
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                }
              >
                Dashboard
              </Button>

              <button
                onClick={toggleTheme}
                className={`p-2 rounded-full ${
                  theme === 'dark'
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 focus:ring-offset-gray-800'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-offset-white'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200`}
                aria-label="Toggle dark mode"
              >
                {theme === 'dark' ? (
                  <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                )}
              </button>

              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className={`flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    theme === 'dark'
                      ? 'focus:ring-offset-gray-800'
                      : 'focus:ring-offset-white'
                  } focus:ring-blue-500 rounded-full transition-all duration-200`}
                >
                  <div className={`flex items-center space-x-2 ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                      : 'bg-gray-200 border-gray-300 hover:bg-gray-300'
                  } px-3 py-2 rounded-full shadow-sm border transition-all duration-200`}>
                    <div className="w-7 h-7 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                      {user?.email?.charAt(0).toUpperCase() || 'U'}
                    </div>
                    <span className={`text-sm font-medium max-w-[120px] truncate hidden sm:block ${
                      theme === 'dark' ? 'text-gray-200' : 'text-gray-700'
                    }`}>
                      {user?.email}
                    </span>
                  </div>
                </button>

                {isUserMenuOpen && (
                  <div className={`absolute right-0 mt-2 w-56 rounded-xl shadow-lg py-1 ${
                    theme === 'dark'
                      ? 'bg-gray-800 ring-gray-700'
                      : 'bg-white ring-gray-200'
                  } ring-1 ring-opacity-5 z-10 animate-fade-in`}>
                    <div className={`px-4 py-3 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>Signed in as</p>
                      <p className={`text-sm font-medium truncate ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>{user?.email}</p>
                    </div>
                    <Link
                      href="/dashboard/profile"
                      className={`flex items-center px-4 py-2 text-sm ${
                        theme === 'dark'
                          ? 'text-gray-300 hover:bg-gray-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <svg className={`mr-3 h-5 w-5 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Profile
                    </Link>
                    <Link
                      href="/dashboard/settings"
                      className={`flex items-center px-4 py-2 text-sm ${
                        theme === 'dark'
                          ? 'text-gray-300 hover:bg-gray-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <svg className={`mr-3 h-5 w-5 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      Settings
                    </Link>
                    <div className={`border-t mt-1 pt-1 ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
                      <button
                        onClick={handleLogout}
                        className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                          theme === 'dark'
                            ? 'text-red-400 hover:bg-gray-700'
                            : 'text-red-600 hover:bg-gray-100'
                        }`}
                      >
                        <svg className={`mr-3 h-5 w-5 ${theme === 'dark' ? 'text-red-400' : 'text-red-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Sign out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile navigation - visible only on small screens */}
      <div className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b md:hidden`}>
        <div className="px-4 py-3">
          <div className="flex justify-between space-x-4">
            <Link
              href="/trading"
              className={`flex-1 text-center py-2 text-sm font-medium rounded-md ${
                pathname === '/trading'
                  ? theme === 'dark'
                    ? 'bg-gray-700 text-white'
                    : 'bg-gray-100 text-blue-600'
                  : theme === 'dark'
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              Market
            </Link>
            <Link
              href="/trading/positions"
              className={`flex-1 text-center py-2 text-sm font-medium rounded-md ${
                pathname === '/trading/positions'
                  ? theme === 'dark'
                    ? 'bg-gray-700 text-white'
                    : 'bg-gray-100 text-blue-600'
                  : theme === 'dark'
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              Positions
            </Link>
            <Link
              href="/trading/history"
              className={`flex-1 text-center py-2 text-sm font-medium rounded-md ${
                pathname === '/trading/history'
                  ? theme === 'dark'
                    ? 'bg-gray-700 text-white'
                    : 'bg-gray-100 text-blue-600'
                  : theme === 'dark'
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              History
            </Link>
          </div>
        </div>
      </div>

      {/* Main content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
