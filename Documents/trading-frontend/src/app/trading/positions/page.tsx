'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '../../../context/ThemeContext';
import { useAuth } from '../../../context/AuthContext';
import { ProfessionalTradingApiService, Position } from '../../../lib/api/professional-trading';
import PositionsPanel from '../../../components/trading/PositionsPanel';

export default function PositionsPage() {
  const { theme } = useTheme();
  const { isAuthenticated, authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }
  }, [authLoading, isAuthenticated, router]);

  if (authLoading) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header */}
      <div className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Positions Management
            </h1>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
              Monitor your open positions, track P&L, and manage risk
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => router.push('/trading')}
              className={`px-4 py-2 rounded-md border transition-colors ${
                theme === 'dark'
                  ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                  : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Back to Trading
            </button>

            <button
              onClick={() => router.push('/trading/orders')}
              className={`px-4 py-2 rounded-md border transition-colors ${
                theme === 'dark'
                  ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                  : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              View Orders
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} h-[calc(100vh-200px)]`}>
          <PositionsPanel theme={theme} />
        </div>
      </div>
    </div>
  );
}
