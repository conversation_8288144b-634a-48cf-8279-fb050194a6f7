'use client';

import { TradingAccount } from '../../api/wallets';
import Link from 'next/link';

interface AccountCardProps {
  accounts: TradingAccount[];
  showViewAll?: boolean;
  viewAllLink?: string;
}

export default function AccountCard({ accounts, showViewAll = true, viewAllLink = '/dashboard/accounts' }: AccountCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'CLOSED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">Trading Accounts</h3>
        <div className="flex space-x-2">
          <Link href="/accounts/create" className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
            Add Account
          </Link>
          {showViewAll && (
            <Link href={viewAllLink} className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
              View all
            </Link>
          )}
        </div>
      </div>
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {accounts.length === 0 ? (
            <li className="px-4 py-4 sm:px-6 text-center text-gray-500 dark:text-gray-400">
              <p>You don't have any trading accounts yet.</p>
              <Link href="/accounts/create" className="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                Create your first trading account
              </Link>
            </li>
          ) : (
            accounts.map((account) => (
              <li key={account.id} className="px-4 py-4 sm:px-6">
                <Link href={`/trading/${account.id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 -my-4 px-4 py-4 sm:-mx-6 sm:px-6 transition-colors duration-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {account.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Account: {account.account_number}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Wallet: {account.wallet.name} ({account.wallet.currency})
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(account.status)}`}>
                        {account.status}
                      </span>
                      <span className="ml-4 text-sm text-gray-500 dark:text-gray-400">
                        Leverage: {account.leverage}x
                      </span>
                    </div>
                  </div>
                </Link>
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  );
}
