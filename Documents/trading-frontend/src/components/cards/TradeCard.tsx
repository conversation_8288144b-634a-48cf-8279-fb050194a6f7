'use client';

import { Trade } from '../../api/trading';
import Link from 'next/link';

interface TradeCardProps {
  trades: Trade[];
  showViewAll?: boolean;
  viewAllLink?: string;
}

export default function TradeCard({ trades, showViewAll = true, viewAllLink = '/dashboard/trades' }: TradeCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'CLOSED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'PENDING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getProfitLossColor = (profitLoss: string) => {
    const value = parseFloat(profitLoss);
    if (value > 0) {
      return 'text-green-600 dark:text-green-400';
    } else if (value < 0) {
      return 'text-red-600 dark:text-red-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">Recent Trades</h3>
        <div className="flex space-x-2">
          <Link href="/trading" className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
            Trade Now
          </Link>
          {showViewAll && (
            <Link href={viewAllLink} className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
              View all
            </Link>
          )}
        </div>
      </div>
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {trades.length === 0 ? (
            <li className="px-4 py-4 sm:px-6 text-center text-gray-500 dark:text-gray-400">
              <p>You haven't made any trades yet.</p>
              <Link href="/trading" className="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                Start trading
              </Link>
            </li>
          ) : (
            trades.map((trade) => (
              <li key={trade.id} className="px-4 py-4 sm:px-6">
                <Link href={`/trades/${trade.id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 -my-4 px-4 py-4 sm:-mx-6 sm:px-6 transition-colors duration-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {trade.asset.symbol} - {trade.trade_type}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {new Date(trade.open_time).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Account: {trade.account.account_number}
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(trade.status)}`}>
                        {trade.status}
                      </span>
                      <span className={`mt-1 text-sm font-medium ${getProfitLossColor(trade.profit_loss)}`}>
                        {parseFloat(trade.profit_loss).toFixed(2)}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        Volume: {trade.volume} x {trade.leverage}
                      </span>
                    </div>
                  </div>
                </Link>
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  );
}
