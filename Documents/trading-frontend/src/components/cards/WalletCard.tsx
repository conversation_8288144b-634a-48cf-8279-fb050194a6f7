'use client';

import { Wallet } from '../../api/wallets';
import Link from 'next/link';

interface WalletCardProps {
  wallets: Wallet[];
  showViewAll?: boolean;
  viewAllLink?: string;
}

export default function WalletCard({ wallets, showViewAll = true, viewAllLink = '/dashboard/wallets' }: WalletCardProps) {
  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      default:
        return currency;
    }
  };

  const getCurrencyColor = (currency: string) => {
    switch (currency) {
      case 'USD':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'EUR':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'GBP':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'JPY':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'BTC':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'ETH':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">Your Wallets</h3>
        <div className="flex space-x-2">
          <Link href="/wallets/create" className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
            Add Wallet
          </Link>
          {showViewAll && (
            <Link href={viewAllLink} className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
              View all
            </Link>
          )}
        </div>
      </div>
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {wallets.length === 0 ? (
            <li className="px-4 py-4 sm:px-6 text-center text-gray-500 dark:text-gray-400">
              <p>You don't have any wallets yet.</p>
              <Link href="/wallets/create" className="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                Create your first wallet
              </Link>
            </li>
          ) : (
            wallets.map((wallet) => (
              <li key={wallet.id} className="px-4 py-4 sm:px-6">
                <Link href={`/wallets/${wallet.id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 -my-4 px-4 py-4 sm:-mx-6 sm:px-6 transition-colors duration-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 h-10 w-10 rounded-full ${getCurrencyColor(wallet.currency)} flex items-center justify-center text-xl font-bold`}>
                        {getCurrencyIcon(wallet.currency)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {wallet.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {wallet.currency}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {parseFloat(wallet.balance).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 })} {wallet.currency}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {wallet.is_active ? 'Active' : 'Inactive'}
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  );
}
