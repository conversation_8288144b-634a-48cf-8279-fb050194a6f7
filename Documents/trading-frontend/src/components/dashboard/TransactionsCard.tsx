'use client';

import React from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { useTheme } from '../../context/ThemeContext';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdraw';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed';
  date: string;
}

interface TransactionsCardProps {
  transactions?: Transaction[];
  isLoading?: boolean;
}

export default function TransactionsCard({
  transactions = [],
  isLoading = false
}: TransactionsCardProps) {
  const { themeColors } = useTheme();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/30';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/30';
      case 'failed':
        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    if (type === 'deposit') {
      return (
        <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </div>
      );
    } else {
      return (
        <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      );
    }
  };

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center py-8 sm:py-12">
      <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
        <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      </div>
      <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white mb-2">No transactions yet</h3>
      <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 text-center mb-6 px-4">
        Make your first deposit or withdrawal to see your transactions here.
      </p>
      <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
        <Button href="/deposit" variant="primary" size="sm">
          Make a Deposit
        </Button>
        <Button href="/withdraw" variant="outline" size="sm">
          Withdraw Funds
        </Button>
      </div>
    </div>
  );

  const renderLoadingState = () => (
    <div className="space-y-4 py-4">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center space-x-4 animate-pulse">
          <div className="rounded-full bg-gray-200 dark:bg-gray-700 h-10 w-10"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
        </div>
      ))}
    </div>
  );

  const renderTransactions = () => (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {transactions.map((transaction) => (
        <div key={transaction.id} className="py-4 flex items-center">
          {getTypeIcon(transaction.type)}
          <div className="ml-2 sm:ml-4 flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {transaction.type === 'deposit' ? 'Deposit' : 'Withdrawal'}
              </p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 sm:mt-0 w-fit ${getStatusColor(transaction.status)}`}>
                {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
              </span>
            </div>
            <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">
              {new Date(transaction.date).toLocaleDateString()}
            </p>
          </div>
          <div className="ml-2 sm:ml-4 text-right">
            <span className={`text-xs sm:text-sm font-medium ${transaction.type === 'deposit' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
              {transaction.type === 'deposit' ? '+' : '-'}{transaction.amount} {transaction.currency}
            </span>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <Card
      title="Transactions"
      subtitle="Your recent deposits and withdrawals"
      icon={
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      }
      footer={
        transactions.length > 0 ? (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Showing {Math.min(transactions.length, 5)} of {transactions.length} transactions
            </span>
            <Button href="/transactions" variant="ghost" size="sm">
              View All
            </Button>
          </div>
        ) : null
      }
    >
      {isLoading ? renderLoadingState() : transactions.length > 0 ? renderTransactions() : renderEmptyState()}
    </Card>
  );
}
