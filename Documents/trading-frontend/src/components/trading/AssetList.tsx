'use client';

import { useState, useEffect, useMemo } from 'react';
import { Asset } from '../../lib/api';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface AssetListProps {
  assets: EnhancedAsset[];
  selectedAsset: Asset | null;
  onAssetSelect: (asset: Asset) => void;
  theme: string;
}

export default function AssetList({ assets, selectedAsset, onAssetSelect, theme }: AssetListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('ALL');

  // Get unique asset types for filter
  const assetTypes = useMemo(() => {
    const types = ['ALL', ...new Set(assets.map(asset => asset.asset_type))];
    return types;
  }, [assets]);

  // Filter assets based on search term and selected filter
  const filteredAssets = useMemo(() => {
    return assets.filter(asset => {
      const matchesSearch = asset.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           asset.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = selectedFilter === 'ALL' || asset.asset_type === selectedFilter;
      return matchesSearch && matchesFilter;
    });
  }, [assets, searchTerm, selectedFilter]);

  // Helper function to format price change with color
  const formatPriceChange = (change: string | number, percentage: string | number) => {
    // Convert to string and handle null/undefined values
    const changeStr = String(change || '0');
    const percentageStr = String(percentage || '0');

    // Check if the change is positive or negative
    const changeNum = parseFloat(changeStr);
    const isPositive = changeNum >= 0;
    const color = isPositive ? 'text-green-500' : 'text-red-500';
    const sign = isPositive ? '+' : '';

    return (
      <div className={`flex flex-col ${color}`}>
        <span className="text-xs">{sign}{changeStr}</span>
        <span className="text-xs">{sign}{percentageStr}%</span>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Search and Filter Section */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        {/* Search Input */}
        <div className="relative mb-3">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className={`h-4 w-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search assets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            }`}
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2">
          {assetTypes.map((type) => (
            <button
              key={type}
              onClick={() => setSelectedFilter(type)}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                selectedFilter === type
                  ? 'bg-blue-600 text-white'
                  : theme === 'dark'
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {type === 'ALL' ? 'All Assets' : type.charAt(0) + type.slice(1).toLowerCase()}
            </button>
          ))}
        </div>
      </div>

      {/* Asset List */}
      <div className={`flex-1 overflow-y-auto divide-y ${theme === 'dark' ? 'divide-gray-700' : 'divide-gray-200'}`}>
        {filteredAssets.length === 0 ? (
          <div className={`p-4 text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
            {searchTerm || selectedFilter !== 'ALL' ? 'No assets match your search' : 'No assets available'}
          </div>
        ) : (
          filteredAssets.map((asset) => (
            <button
              key={asset.id}
              className={`w-full px-4 py-3 flex items-center justify-between ${
                theme === 'dark'
                  ? `hover:bg-gray-700 ${selectedAsset?.id === asset.id ? 'bg-gray-700 border-l-4 border-blue-500' : ''}`
                  : `hover:bg-gray-100 ${selectedAsset?.id === asset.id ? 'bg-gray-100 border-l-4 border-blue-500' : ''}`
              } transition-colors duration-150`}
              onClick={() => onAssetSelect(asset)}
            >
              <div className="flex flex-col items-start">
                <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  {asset.symbol}
                </span>
                <span className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                  {asset.name}
                </span>
                <span className={`text-xs px-2 py-0.5 rounded-full mt-1 ${
                  theme === 'dark' ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                }`}>
                  {asset.asset_type}
                </span>
              </div>
              <div className="flex flex-col items-end">
                <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  ${asset.current_price ? parseFloat(asset.current_price).toLocaleString() : 'N/A'}
                </span>
                {asset.price_change_24h && asset.price_change_percentage_24h &&
                  formatPriceChange(asset.price_change_24h, asset.price_change_percentage_24h)
                }
              </div>
            </button>
          ))
        )}
      </div>
    </div>
  );
}
