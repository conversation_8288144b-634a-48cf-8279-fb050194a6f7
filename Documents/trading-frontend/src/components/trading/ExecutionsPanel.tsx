'use client';

import { useState, useEffect } from 'react';
import { ProfessionalTradingApiService, Execution } from '../../lib/api/professional-trading';

interface ExecutionsPanelProps {
  theme: string;
}

export default function ExecutionsPanel({ theme }: ExecutionsPanelProps) {
  const [executions, setExecutions] = useState<Execution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadExecutions();
  }, [page]);

  const loadExecutions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await ProfessionalTradingApiService.getExecutions({
        page,
        page_size: 10
      });
      
      if (response.success && response.data) {
        setExecutions(response.data.results || []);
        setTotalPages(Math.ceil((response.data.count || 0) / 10));
      }
    } catch (err: any) {
      console.error('Error loading executions:', err);
      setError(err.message || 'Failed to load executions');
    } finally {
      setIsLoading(false);
    }
  };

  const getSideColor = (side: string) => {
    return side === 'BUY'
      ? theme === 'dark' ? 'text-green-400' : 'text-green-600'
      : theme === 'dark' ? 'text-red-400' : 'text-red-600';
  };

  const calculateTotalVolume = () => {
    return executions.reduce((total, execution) => {
      return total + parseFloat(execution.notional_value);
    }, 0);
  };

  const calculateTotalFees = () => {
    return executions.reduce((total, execution) => {
      return total + parseFloat(execution.fee);
    }, 0);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading executions...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Trade Executions
          </h3>
          
          <button
            onClick={loadExecutions}
            className={`px-3 py-1 text-sm rounded-md border transition-colors ${
              theme === 'dark'
                ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                : 'border-gray-300 text-gray-600 hover:bg-gray-100'
            }`}
          >
            Refresh
          </button>
        </div>

        {/* Quick Stats */}
        <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {executions.length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Executions
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              ${calculateTotalVolume().toLocaleString()}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Total Volume
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              ${calculateTotalFees().toFixed(2)}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Total Fees
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900 border-b border-red-400 dark:border-red-600">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Executions List */}
      <div className="flex-1 overflow-y-auto">
        {executions.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                ⚡
              </div>
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                No executions found
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {executions.map((execution) => (
              <div key={execution.id} className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {execution.instrument.symbol}
                      </span>
                      <span className={`text-sm px-2 py-1 rounded ${getSideColor(execution.side)} bg-opacity-20`}>
                        {execution.side}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        theme === 'dark' ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'
                      }`}>
                        EXECUTED
                      </span>
                    </div>
                    
                    <div className="mt-1 text-sm space-y-1">
                      <div className="flex items-center space-x-4">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Price: ${parseFloat(execution.price).toLocaleString()}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Qty: {parseFloat(execution.quantity).toLocaleString()}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Value: ${parseFloat(execution.notional_value).toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Fee: ${parseFloat(execution.fee).toFixed(2)}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Net: ${parseFloat(execution.net_amount).toLocaleString()}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          {new Date(execution.executed_at).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button
                      className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                        theme === 'dark'
                          ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                          : 'border-gray-300 text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      Details
                    </button>
                    
                    <button
                      className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                        theme === 'dark'
                          ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                          : 'border-gray-300 text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      Export
                    </button>
                  </div>
                </div>

                {/* Execution Details */}
                <div className={`mt-3 p-3 rounded-md ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                    <div>
                      <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Order ID</div>
                      <div className={`font-mono ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {execution.order.slice(-8)}
                      </div>
                    </div>
                    <div>
                      <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Execution ID</div>
                      <div className={`font-mono ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {execution.id.slice(-8)}
                      </div>
                    </div>
                    <div>
                      <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Fee Rate</div>
                      <div className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
                        {((parseFloat(execution.fee) / parseFloat(execution.notional_value)) * 100).toFixed(4)}%
                      </div>
                    </div>
                    <div>
                      <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Settlement</div>
                      <div className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
                        T+0
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                page === 1
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Previous
            </button>
            
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              Page {page} of {totalPages}
            </span>
            
            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                page === totalPages
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
