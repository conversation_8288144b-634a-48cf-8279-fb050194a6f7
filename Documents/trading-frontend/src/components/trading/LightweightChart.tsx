'use client';

import { useEffect, useRef, useState } from 'react';
import { Asset, TradingApiService } from '../../lib/api';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface LightweightChartProps {
  asset: EnhancedAsset;
  theme: string;
}

export default function LightweightChart({ asset, theme }: LightweightChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [priceData, setPriceData] = useState<any[]>([]);

  // No need for store actions anymore - we'll use the API service directly

  // Helper function to check if price change is positive
  const isPriceChangePositive = (change: string | number | undefined) => {
    const changeStr = String(change || '0');
    const changeNum = parseFloat(changeStr);
    return changeNum >= 0;
  };

  useEffect(() => {
    const loadChart = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Dynamically import lightweight-charts
        const { createChart } = await import('lightweight-charts');

        if (!chartContainerRef.current) return;

        // Fetch price history
        const historyResponse = await TradingApiService.getPriceHistory({
          symbol: asset.symbol,
          days: 30
        });

        if (!historyResponse.data) {
          throw new Error('No price history data available');
        }

        const chartData = historyResponse.data.map(item => ({
          time: new Date(item.timestamp).getTime() / 1000,
          value: parseFloat(item.price),
          open: parseFloat(item.open_price || item.price),
          high: parseFloat(item.high_price || item.price),
          low: parseFloat(item.low_price || item.price),
          close: parseFloat(item.price)
        })).sort((a, b) => a.time - b.time);

        setPriceData(chartData);

        // Clean up previous chart
        if (chartRef.current) {
          chartRef.current.remove();
        }

        // Create new chart
        const chart = createChart(chartContainerRef.current, {
          width: chartContainerRef.current.clientWidth,
          height: 400,
          layout: {
            backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',
            textColor: theme === 'dark' ? '#d1d5db' : '#374151',
          },
          grid: {
            vertLines: {
              color: theme === 'dark' ? '#374151' : '#e5e7eb',
            },
            horzLines: {
              color: theme === 'dark' ? '#374151' : '#e5e7eb',
            },
          },
          crosshair: {
            mode: 1, // Normal crosshair
          },
          rightPriceScale: {
            borderColor: theme === 'dark' ? '#6b7280' : '#d1d5db',
          },
          timeScale: {
            borderColor: theme === 'dark' ? '#6b7280' : '#d1d5db',
            timeVisible: true,
            secondsVisible: false,
          },
        });

        // Add candlestick series if we have OHLC data
        const hasOHLC = chartData.some(item => 
          item.open !== item.close || item.high !== item.low
        );

        if (hasOHLC) {
          const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#10b981',
            downColor: '#ef4444',
            borderDownColor: '#ef4444',
            borderUpColor: '#10b981',
            wickDownColor: '#6b7280',
            wickUpColor: '#6b7280',
          });

          candlestickSeries.setData(chartData);
        } else {
          // Use line series for simple price data
          const lineSeries = chart.addLineSeries({
            color: theme === 'dark' ? '#3b82f6' : '#2563eb',
            lineWidth: 2,
          });

          const lineData = chartData.map(item => ({
            time: item.time,
            value: item.value
          }));

          lineSeries.setData(lineData);
        }

        chartRef.current = chart;

        // Handle resize
        const handleResize = () => {
          if (chart && chartContainerRef.current) {
            chart.applyOptions({ 
              width: chartContainerRef.current.clientWidth 
            });
          }
        };

        window.addEventListener('resize', handleResize);

        setIsLoading(false);

        return () => {
          window.removeEventListener('resize', handleResize);
          if (chart) {
            chart.remove();
          }
        };

      } catch (err: any) {
        console.error('Error loading lightweight chart:', err);
        setError(err.message || 'Failed to load chart');
        setIsLoading(false);
      }
    };

    loadChart();

    return () => {
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [asset, theme]);

  if (isLoading) {
    return (
      <div className={`h-full flex items-center justify-center ${
        theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'
      } rounded-lg`}>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
            Loading chart...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`h-full flex items-center justify-center ${
        theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'
      } rounded-lg`}>
        <div className="text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            {asset.symbol} Chart
          </h3>
          <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            {asset.name} • {priceData.length} data points
          </p>
        </div>
        <div className="text-right">
          <div className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            ${asset.current_price ? parseFloat(asset.current_price).toLocaleString() : 'N/A'}
          </div>
          {asset.price_change_percentage_24h && (
            <div className={`text-sm ${
              isPriceChangePositive(asset.price_change_24h)
                ? 'text-green-500'
                : 'text-red-500'
            }`}>
              {isPriceChangePositive(asset.price_change_24h) ? '+' : ''}
              {asset.price_change_percentage_24h}%
            </div>
          )}
        </div>
      </div>

      {/* Chart Container */}
      <div className="flex-1">
        <div 
          ref={chartContainerRef} 
          className="w-full h-full rounded-lg"
          style={{ minHeight: '300px' }}
        />
      </div>
    </div>
  );
}
