'use client';

import { useState, useEffect } from 'react';
import { ProfessionalTradingApiService, MarketPrice } from '../../lib/api/professional-trading';

interface MarketDataPanelProps {
  theme: string;
}

export default function MarketDataPanel({ theme }: MarketDataPanelProps) {
  const [marketData, setMarketData] = useState<{ [symbol: string]: MarketPrice }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadMarketData();
    
    // Set up auto-refresh
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(loadMarketData, 30000); // Refresh every 30 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const loadMarketData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await ProfessionalTradingApiService.getLatestMarketPrices();
      
      if (response.success && response.data) {
        setMarketData(response.data);
        setLastUpdate(new Date());
      }
    } catch (err: any) {
      console.error('Error loading market data:', err);
      setError(err.message || 'Failed to load market data');
    } finally {
      setIsLoading(false);
    }
  };

  const getSpreadColor = (spread: string) => {
    const spreadValue = parseFloat(spread);
    if (spreadValue < 0.01) {
      return theme === 'dark' ? 'text-green-400' : 'text-green-600';
    } else if (spreadValue < 0.05) {
      return theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600';
    }
    return theme === 'dark' ? 'text-red-400' : 'text-red-600';
  };

  const formatPrice = (price: string) => {
    const value = parseFloat(price);
    if (value >= 1000) {
      return value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    return value.toFixed(4);
  };

  const calculateSpreadPercentage = (bid: string, ask: string) => {
    const bidValue = parseFloat(bid);
    const askValue = parseFloat(ask);
    const midPrice = (bidValue + askValue) / 2;
    return (((askValue - bidValue) / midPrice) * 100).toFixed(4);
  };

  const sortedSymbols = Object.keys(marketData).sort();

  if (isLoading && Object.keys(marketData).length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading market data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Market Data
          </h3>
          
          <div className="flex items-center space-x-3">
            {/* Auto-refresh toggle */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                Auto-refresh
              </span>
            </label>
            
            <button
              onClick={loadMarketData}
              disabled={isLoading}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                isLoading
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Last Update */}
        {lastUpdate && (
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </div>
        )}

        {/* Quick Stats */}
        <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {sortedSymbols.length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Symbols
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {sortedSymbols.filter(symbol => parseFloat(marketData[symbol].spread) < 0.01).length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Tight Spreads
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {autoRefresh ? '30s' : 'Manual'}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Refresh Rate
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900 border-b border-red-400 dark:border-red-600">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Market Data List */}
      <div className="flex-1 overflow-y-auto">
        {sortedSymbols.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                📈
              </div>
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                No market data available
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {sortedSymbols.map((symbol) => {
              const data = marketData[symbol];
              return (
                <div key={symbol} className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors`}>
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                          {symbol}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded ${
                          theme === 'dark' ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                        }`}>
                          LIVE
                        </span>
                      </div>
                      
                      <div className="mt-1 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Bid</div>
                          <div className={`font-mono ${theme === 'dark' ? 'text-green-400' : 'text-green-600'}`}>
                            ${formatPrice(data.bid)}
                          </div>
                        </div>
                        
                        <div>
                          <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Ask</div>
                          <div className={`font-mono ${theme === 'dark' ? 'text-red-400' : 'text-red-600'}`}>
                            ${formatPrice(data.ask)}
                          </div>
                        </div>
                        
                        <div>
                          <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Last</div>
                          <div className={`font-mono ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                            ${formatPrice(data.last_price)}
                          </div>
                        </div>
                        
                        <div>
                          <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Spread</div>
                          <div className={`font-mono ${getSpreadColor(data.spread)}`}>
                            ${parseFloat(data.spread).toFixed(4)} ({calculateSpreadPercentage(data.bid, data.ask)}%)
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex items-center space-x-2">
                      <button
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          theme === 'dark'
                            ? 'bg-green-600 hover:bg-green-700 text-white'
                            : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                      >
                        BUY
                      </button>
                      
                      <button
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          theme === 'dark'
                            ? 'bg-red-600 hover:bg-red-700 text-white'
                            : 'bg-red-600 hover:bg-red-700 text-white'
                        }`}
                      >
                        SELL
                      </button>
                    </div>
                  </div>

                  {/* Price Chart Placeholder */}
                  <div className={`mt-3 p-3 rounded-md ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center justify-between text-xs">
                      <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                        Mid Price: ${((parseFloat(data.bid) + parseFloat(data.ask)) / 2).toFixed(4)}
                      </span>
                      <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                        Updated: {new Date(data.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    
                    {/* Simple price visualization */}
                    <div className="mt-2 h-8 bg-gray-200 dark:bg-gray-600 rounded relative overflow-hidden">
                      <div 
                        className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-500 to-red-500 opacity-30"
                        style={{ width: '100%' }}
                      ></div>
                      <div 
                        className="absolute top-0 w-1 h-full bg-blue-500"
                        style={{ 
                          left: `${((parseFloat(data.last_price) - parseFloat(data.bid)) / (parseFloat(data.ask) - parseFloat(data.bid))) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between text-xs">
          <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
            Real-time market data
          </span>
          <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
            {isLoading && <span className="animate-pulse">●</span>} 
            {autoRefresh ? 'Auto-updating' : 'Manual refresh'}
          </span>
        </div>
      </div>
    </div>
  );
}
