'use client';

import { useState, useEffect } from 'react';
import { ProfessionalTradingApiService, Order } from '../../lib/api/professional-trading';

interface OrdersPanelProps {
  theme: string;
}

export default function OrdersPanel({ theme }: OrdersPanelProps) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [openOrders, setOpenOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'open' | 'filled' | 'cancelled'>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadOrders();
    loadOpenOrders();
  }, [page, filter]);

  const loadOrders = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params: any = { page, page_size: 10 };
      
      if (filter !== 'all') {
        params.status = filter.toUpperCase();
      }

      const response = await ProfessionalTradingApiService.getOrders(params);
      
      if (response.success && response.data) {
        setOrders(response.data.results || []);
        setTotalPages(Math.ceil((response.data.count || 0) / 10));
      }
    } catch (err: any) {
      console.error('Error loading orders:', err);
      setError(err.message || 'Failed to load orders');
    } finally {
      setIsLoading(false);
    }
  };

  const loadOpenOrders = async () => {
    try {
      const response = await ProfessionalTradingApiService.getOpenOrders();
      if (response.success && response.data) {
        setOpenOrders(response.data);
      }
    } catch (err: any) {
      console.error('Error loading open orders:', err);
    }
  };

  const handleCancelOrder = async (orderId: string) => {
    if (!confirm('Are you sure you want to cancel this order?')) {
      return;
    }

    try {
      const response = await ProfessionalTradingApiService.cancelOrder(orderId, 'User cancelled');
      
      if (response.success) {
        // Refresh orders
        await loadOrders();
        await loadOpenOrders();
      }
    } catch (err: any) {
      console.error('Error cancelling order:', err);
      setError(err.message || 'Failed to cancel order');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600';
      case 'FILLED':
        return theme === 'dark' ? 'text-green-400' : 'text-green-600';
      case 'PARTIALLY_FILLED':
        return theme === 'dark' ? 'text-blue-400' : 'text-blue-600';
      case 'CANCELLED':
        return theme === 'dark' ? 'text-red-400' : 'text-red-600';
      case 'REJECTED':
        return theme === 'dark' ? 'text-red-400' : 'text-red-600';
      default:
        return theme === 'dark' ? 'text-gray-400' : 'text-gray-600';
    }
  };

  const getSideColor = (side: string) => {
    return side === 'BUY'
      ? theme === 'dark' ? 'text-green-400' : 'text-green-600'
      : theme === 'dark' ? 'text-red-400' : 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading orders...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Orders
          </h3>
          
          {/* Filter */}
          <select
            value={filter}
            onChange={(e) => {
              setFilter(e.target.value as any);
              setPage(1);
            }}
            className={`text-sm rounded-md border ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value="all">All Orders</option>
            <option value="open">Open Orders</option>
            <option value="filled">Filled Orders</option>
            <option value="cancelled">Cancelled Orders</option>
          </select>
        </div>

        {/* Quick Stats */}
        <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {openOrders.length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Open
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {orders.filter(o => o.status === 'FILLED').length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Filled
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {orders.filter(o => o.status === 'CANCELLED').length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Cancelled
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900 border-b border-red-400 dark:border-red-600">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Orders List */}
      <div className="flex-1 overflow-y-auto">
        {orders.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                📋
              </div>
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                No orders found
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {orders.map((order) => (
              <div key={order.id} className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {order.instrument.symbol}
                      </span>
                      <span className={`text-sm px-2 py-1 rounded ${getSideColor(order.side)} bg-opacity-20`}>
                        {order.side}
                      </span>
                      <span className={`text-sm px-2 py-1 rounded ${getStatusColor(order.status)} bg-opacity-20`}>
                        {order.status.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <div className="mt-1 text-sm space-y-1">
                      <div className="flex items-center space-x-4">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Type: {order.order_type.replace('_', ' ')}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Qty: {parseFloat(order.quantity).toLocaleString()}
                        </span>
                        {order.price && (
                          <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                            Price: ${parseFloat(order.price).toLocaleString()}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Filled: {order.fill_percentage.toFixed(1)}%
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          {new Date(order.created_at).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {(order.status === 'PENDING' || order.status === 'PARTIALLY_FILLED') && (
                      <button
                        onClick={() => handleCancelOrder(order.id)}
                        className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                          theme === 'dark'
                            ? 'border-red-600 text-red-400 hover:bg-red-600 hover:text-white'
                            : 'border-red-600 text-red-600 hover:bg-red-600 hover:text-white'
                        }`}
                      >
                        Cancel
                      </button>
                    )}
                    
                    <button
                      className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                        theme === 'dark'
                          ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                          : 'border-gray-300 text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      Details
                    </button>
                  </div>
                </div>

                {/* Progress Bar for Partially Filled Orders */}
                {order.status === 'PARTIALLY_FILLED' && (
                  <div className="mt-3">
                    <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2`}>
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${order.fill_percentage}%` }}
                      ></div>
                    </div>
                    <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      {parseFloat(order.filled_quantity).toLocaleString()} / {parseFloat(order.quantity).toLocaleString()} filled
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                page === 1
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Previous
            </button>
            
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              Page {page} of {totalPages}
            </span>
            
            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                page === totalPages
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
