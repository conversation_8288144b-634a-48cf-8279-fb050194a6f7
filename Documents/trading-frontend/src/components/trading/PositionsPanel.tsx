'use client';

import { useState, useEffect } from 'react';
import { ProfessionalTradingApiService, Position } from '../../lib/api/professional-trading';
import { priceChangeService, PriceChangeService } from '../../lib/services/priceChangeService';

interface PositionsPanelProps {
  theme: string;
}

export default function PositionsPanel({ theme }: PositionsPanelProps) {
  const [positions, setPositions] = useState<Position[]>([]);
  const [openPositions, setOpenPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'open' | 'closed'>('open');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadPositions();
    loadOpenPositions();
  }, [page, filter]);

  const loadPositions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params: any = { page, page_size: 10 };
      
      if (filter !== 'all') {
        params.status = filter.toUpperCase();
      }

      const response = await ProfessionalTradingApiService.getPositions(params);
      
      if (response.success && response.data) {
        setPositions(response.data.results || []);
        setTotalPages(Math.ceil((response.data.count || 0) / 10));
      }
    } catch (err: any) {
      console.error('Error loading positions:', err);
      setError(err.message || 'Failed to load positions');
    } finally {
      setIsLoading(false);
    }
  };

  const loadOpenPositions = async () => {
    try {
      const response = await ProfessionalTradingApiService.getOpenPositions();
      if (response.success && response.data) {
        setOpenPositions(response.data);
      }
    } catch (err: any) {
      console.error('Error loading open positions:', err);
    }
  };

  const handleClosePosition = async (positionId: string, quantity?: string) => {
    if (!confirm('Are you sure you want to close this position?')) {
      return;
    }

    try {
      const response = await ProfessionalTradingApiService.closePosition(positionId, {
        quantity,
        reason: 'User closed position'
      });
      
      if (response.success) {
        // Refresh positions
        await loadPositions();
        await loadOpenPositions();
      }
    } catch (err: any) {
      console.error('Error closing position:', err);
      setError(err.message || 'Failed to close position');
    }
  };

  const getPnLColor = (pnl: string) => {
    const value = parseFloat(pnl);
    if (value > 0) {
      return theme === 'dark' ? 'text-green-400' : 'text-green-600';
    } else if (value < 0) {
      return theme === 'dark' ? 'text-red-400' : 'text-red-600';
    }
    return theme === 'dark' ? 'text-gray-400' : 'text-gray-600';
  };

  const getSideColor = (side: string) => {
    return side === 'LONG'
      ? theme === 'dark' ? 'text-green-400' : 'text-green-600'
      : theme === 'dark' ? 'text-red-400' : 'text-red-600';
  };

  const calculateTotalPnL = () => {
    return openPositions.reduce((total, position) => {
      return total + parseFloat(position.unrealized_pnl);
    }, 0);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading positions...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Positions
          </h3>
          
          {/* Filter */}
          <select
            value={filter}
            onChange={(e) => {
              setFilter(e.target.value as any);
              setPage(1);
            }}
            className={`text-sm rounded-md border ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value="all">All Positions</option>
            <option value="open">Open Positions</option>
            <option value="closed">Closed Positions</option>
          </select>
        </div>

        {/* Quick Stats */}
        <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {openPositions.length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Open
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${getPnLColor(calculateTotalPnL().toString())}`}>
              ${calculateTotalPnL().toFixed(2)}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Total P&L
            </div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              {positions.filter(p => p.status === 'CLOSED').length}
            </div>
            <div className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
              Closed
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900 border-b border-red-400 dark:border-red-600">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Positions List */}
      <div className="flex-1 overflow-y-auto">
        {positions.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                📊
              </div>
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                No positions found
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {positions.map((position) => (
              <div key={position.id} className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {position.instrument.symbol}
                      </span>
                      <span className={`text-sm px-2 py-1 rounded ${getSideColor(position.side)} bg-opacity-20`}>
                        {position.side}
                      </span>
                      <span className={`text-sm px-2 py-1 rounded ${
                        position.status === 'OPEN'
                          ? theme === 'dark' ? 'text-green-400 bg-green-900' : 'text-green-600 bg-green-100'
                          : theme === 'dark' ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-100'
                      }`}>
                        {position.status}
                      </span>
                    </div>
                    
                    <div className="mt-1 text-sm space-y-1">
                      <div className="flex items-center space-x-4">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Entry: ${parseFloat(position.entry_price).toLocaleString()}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Current: ${parseFloat(position.current_price).toLocaleString()}
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          Qty: {parseFloat(position.quantity).toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <span className={`font-medium ${getPnLColor(position.unrealized_pnl)}`}>
                          P&L: ${parseFloat(position.unrealized_pnl).toFixed(2)} ({position.pnl_percentage.toFixed(2)}%)
                        </span>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                          {new Date(position.opened_at).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {position.status === 'OPEN' && (
                      <>
                        <button
                          onClick={() => handleClosePosition(position.id, (parseFloat(position.quantity) / 2).toString())}
                          className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                            theme === 'dark'
                              ? 'border-yellow-600 text-yellow-400 hover:bg-yellow-600 hover:text-white'
                              : 'border-yellow-600 text-yellow-600 hover:bg-yellow-600 hover:text-white'
                          }`}
                        >
                          Close 50%
                        </button>
                        
                        <button
                          onClick={() => handleClosePosition(position.id)}
                          className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                            theme === 'dark'
                              ? 'border-red-600 text-red-400 hover:bg-red-600 hover:text-white'
                              : 'border-red-600 text-red-600 hover:bg-red-600 hover:text-white'
                          }`}
                        >
                          Close All
                        </button>
                      </>
                    )}
                    
                    <button
                      className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                        theme === 'dark'
                          ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                          : 'border-gray-300 text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      Details
                    </button>
                  </div>
                </div>

                {/* P&L Progress Bar */}
                {position.status === 'OPEN' && (
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs mb-1">
                      <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
                        Performance
                      </span>
                      <span className={getPnLColor(position.pnl_percentage.toString())}>
                        {position.pnl_percentage > 0 ? '+' : ''}{position.pnl_percentage.toFixed(2)}%
                      </span>
                    </div>
                    <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2`}>
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          position.pnl_percentage >= 0 ? 'bg-green-500' : 'bg-red-500'
                        }`}
                        style={{ 
                          width: `${Math.min(Math.abs(position.pnl_percentage), 100)}%`,
                          marginLeft: position.pnl_percentage < 0 ? `${100 - Math.min(Math.abs(position.pnl_percentage), 100)}%` : '0'
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                page === 1
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Previous
            </button>
            
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              Page {page} of {totalPages}
            </span>
            
            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                page === totalPages
                  ? theme === 'dark'
                    ? 'border-gray-700 text-gray-600 cursor-not-allowed'
                    : 'border-gray-200 text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white'
                    : 'border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
