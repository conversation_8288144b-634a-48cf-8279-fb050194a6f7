'use client';

import { useState, useEffect } from 'react';
import { Asset, TradingAccount } from '../../lib/api';
import { ProfessionalTradingApiService } from '../../lib/api/professional-trading';
import Button from '../ui/Button';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface ProfessionalOrderFormProps {
  asset: EnhancedAsset;
  accounts: TradingAccount[];
  theme: string;
}

export default function ProfessionalOrderForm({ asset, accounts, theme }: ProfessionalOrderFormProps) {
  const [selectedAccount, setSelectedAccount] = useState(accounts[0]?.id || '');
  const [orderType, setOrderType] = useState<'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT'>('MARKET');
  const [side, setSide] = useState<'BUY' | 'SELL'>('BUY');
  const [quantity, setQuantity] = useState('100');
  const [price, setPrice] = useState('');
  const [stopPrice, setStopPrice] = useState('');
  const [timeInForce, setTimeInForce] = useState<'GTC' | 'IOC' | 'FOK' | 'DAY'>('GTC');
  const [leverage, setLeverage] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Auto-fill price for limit orders
  useEffect(() => {
    if (orderType === 'LIMIT' && asset.current_price && !price) {
      setPrice(asset.current_price);
    }
  }, [orderType, asset.current_price, price]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate required fields
      if (!selectedAccount) {
        throw new Error('Please select a trading account');
      }

      if (!quantity || parseFloat(quantity) <= 0) {
        throw new Error('Please enter a valid quantity');
      }

      if ((orderType === 'LIMIT' || orderType === 'STOP_LIMIT') && (!price || parseFloat(price) <= 0)) {
        throw new Error('Please enter a valid price for limit orders');
      }

      if ((orderType === 'STOP' || orderType === 'STOP_LIMIT') && (!stopPrice || parseFloat(stopPrice) <= 0)) {
        throw new Error('Please enter a valid stop price');
      }

      // Prepare order data
      const orderData = {
        account_id: selectedAccount,
        instrument_id: asset.id,
        order_type: orderType,
        side,
        quantity,
        time_in_force: timeInForce,
        leverage,
        ...(orderType === 'LIMIT' || orderType === 'STOP_LIMIT' ? { price } : {}),
        ...(orderType === 'STOP' || orderType === 'STOP_LIMIT' ? { stop_price: stopPrice } : {}),
      };

      // Log the order data being sent for backend configuration
      console.log('🚀 TRADING ORDER DATA BEING SENT:', {
        orderType,
        side,
        asset: {
          id: asset.id,
          symbol: asset.symbol,
          name: asset.name,
          asset_type: asset.asset_type
        },
        orderData,
        calculatedValues: {
          notionalValue: calculateNotionalValue(),
          requiredMargin: calculateMargin(),
          currentPrice: asset.current_price,
        }
      });

      const response = await ProfessionalTradingApiService.placeOrder(orderData);

      if (response.data) {
        setSuccess(`${side} order placed successfully for ${quantity} ${asset.symbol}`);

        // Reset form for market orders, keep values for limit orders
        if (orderType === 'MARKET') {
          setQuantity('100');
        }
      }

    } catch (err: any) {
      console.error('Order placement error:', err);
      setError(err.message || 'Failed to place order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateNotionalValue = () => {
    const qty = parseFloat(quantity) || 0;
    const currentPrice = parseFloat(asset.current_price || '0') || 0;
    const orderPrice = orderType === 'MARKET' ? currentPrice : parseFloat(price) || currentPrice;
    return (qty * orderPrice).toFixed(2);
  };

  const calculateMargin = () => {
    const notional = parseFloat(calculateNotionalValue());
    return (notional / leverage).toFixed(2);
  };

  return (
    <div className="space-y-4">
      {/* Success/Error Messages */}
      {success && (
        <div className="p-3 bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 rounded-md">
          <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
        </div>
      )}

      {error && (
        <div className="p-3 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 rounded-md">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Account Selection */}
        <div>
          <label htmlFor="account" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Trading Account
          </label>
          <select
            id="account"
            value={selectedAccount}
            onChange={(e) => setSelectedAccount(e.target.value)}
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
            required
          >
            {accounts.map((account) => (
              <option key={account.id} value={account.id}>
                {account.name} - ${parseFloat(account.balance || '0').toLocaleString()}
              </option>
            ))}
          </select>
        </div>

        {/* Order Type */}
        <div>
          <label className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
            Order Type
          </label>
          <div className="grid grid-cols-2 gap-2">
            {['MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT'].map((type) => (
              <button
                key={type}
                type="button"
                onClick={() => setOrderType(type as any)}
                className={`py-2 px-3 text-sm rounded-md border transition-colors ${
                  orderType === type
                    ? theme === 'dark'
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'bg-blue-600 border-blue-600 text-white'
                    : theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                {type.replace('_', ' ')}
              </button>
            ))}
          </div>
        </div>

        {/* Buy/Sell Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <button
            type="button"
            onClick={() => setSide('BUY')}
            className={`py-3 px-4 rounded-md font-medium transition-colors ${
              side === 'BUY'
                ? 'bg-green-600 text-white'
                : theme === 'dark'
                  ? 'bg-gray-700 border border-gray-600 text-gray-300 hover:bg-gray-600'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            BUY
          </button>
          <button
            type="button"
            onClick={() => setSide('SELL')}
            className={`py-3 px-4 rounded-md font-medium transition-colors ${
              side === 'SELL'
                ? 'bg-red-600 text-white'
                : theme === 'dark'
                  ? 'bg-gray-700 border border-gray-600 text-gray-300 hover:bg-gray-600'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            SELL
          </button>
        </div>

        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Quantity
          </label>
          <input
            type="number"
            id="quantity"
            value={quantity}
            onChange={(e) => setQuantity(e.target.value)}
            step="0.00000001"
            min="0"
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
            required
          />
        </div>

        {/* Price (for LIMIT and STOP_LIMIT orders) */}
        {(orderType === 'LIMIT' || orderType === 'STOP_LIMIT') && (
          <div>
            <label htmlFor="price" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
              Limit Price
            </label>
            <input
              type="number"
              id="price"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              step="0.01"
              min="0"
              className={`w-full ${
                theme === 'dark'
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              required
            />
          </div>
        )}

        {/* Stop Price (for STOP and STOP_LIMIT orders) */}
        {(orderType === 'STOP' || orderType === 'STOP_LIMIT') && (
          <div>
            <label htmlFor="stopPrice" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
              Stop Price
            </label>
            <input
              type="number"
              id="stopPrice"
              value={stopPrice}
              onChange={(e) => setStopPrice(e.target.value)}
              step="0.01"
              min="0"
              className={`w-full ${
                theme === 'dark'
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              required
            />
          </div>
        )}

        {/* Time in Force */}
        <div>
          <label htmlFor="timeInForce" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Time in Force
          </label>
          <select
            id="timeInForce"
            value={timeInForce}
            onChange={(e) => setTimeInForce(e.target.value as any)}
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value="GTC">Good Till Cancelled (GTC)</option>
            <option value="IOC">Immediate or Cancel (IOC)</option>
            <option value="FOK">Fill or Kill (FOK)</option>
            <option value="DAY">Day Order</option>
          </select>
        </div>

        {/* Leverage */}
        <div>
          <label htmlFor="leverage" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Leverage: {leverage}x
          </label>
          <input
            type="range"
            id="leverage"
            value={leverage}
            onChange={(e) => setLeverage(parseInt(e.target.value))}
            min="1"
            max="100"
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>1x</span>
            <span>50x</span>
            <span>100x</span>
          </div>
        </div>

        {/* Order Summary */}
        <div className={`p-3 rounded-md ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <h4 className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-2`}>
            Order Summary
          </h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Notional Value:</span>
              <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>${calculateNotionalValue()}</span>
            </div>
            <div className="flex justify-between">
              <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Required Margin:</span>
              <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>${calculateMargin()}</span>
            </div>
            <div className="flex justify-between">
              <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Current Price:</span>
              <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
                ${asset.current_price ? parseFloat(asset.current_price).toLocaleString() : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isSubmitting}
          className={`w-full py-3 ${
            side === 'BUY' 
              ? 'bg-green-600 hover:bg-green-700' 
              : 'bg-red-600 hover:bg-red-700'
          } text-white font-medium`}
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Placing Order...
            </div>
          ) : (
            `${side} ${asset.symbol}`
          )}
        </Button>
      </form>
    </div>
  );
}
