'use client';

import { useState } from 'react';
import { Asset, TradingAccount, TradingApiService, ApiError } from '../../lib/api';
import Button from '../ui/Button';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface TradeFormProps {
  asset: EnhancedAsset;
  accounts: TradingAccount[];
  theme: string;
}

export default function TradeForm({ asset, accounts, theme }: TradeFormProps) {
  const [selectedAccount, setSelectedAccount] = useState(accounts[0]?.id || '');
  const [tradeType, setTradeType] = useState<'BUY' | 'SELL'>('BUY');
  const [amount, setAmount] = useState('100');
  const [leverage, setLeverage] = useState(1);
  const [takeProfit, setTakeProfit] = useState('');
  const [stopLoss, setStopLoss] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      await TradingApiService.placeTrade({
        account_id: selectedAccount,
        asset_id: asset.id,
        trade_type: tradeType,
        order_type: 'MARKET',
        volume: amount,
        leverage,
        take_profit: takeProfit || undefined,
        stop_loss: stopLoss || undefined,
      });

      // Show success message
      alert(`${tradeType} order placed for ${amount} ${asset.symbol} with ${leverage}x leverage`);

      // Reset form
      setAmount('100');
      setTakeProfit('');
      setStopLoss('');

    } catch (err) {
      const errorMessage = err instanceof ApiError
        ? err.message
        : 'Failed to place trade. Please try again.';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-full">
      <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-4`}>Place Order</h3>

      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Account selection */}
        <div>
          <label htmlFor="account" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Trading Account
          </label>
          <select
            id="account"
            value={selectedAccount}
            onChange={(e) => setSelectedAccount(e.target.value)}
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
            required
          >
            {accounts.map((account) => (
              <option key={account.id} value={account.id}>
                {account.name} ({account.account_number})
              </option>
            ))}
          </select>
        </div>

        {/* Trade type */}
        <div>
          <label className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Direction
          </label>
          <div className="flex space-x-2">
            <button
              type="button"
              className={`flex-1 py-2 px-4 rounded-md font-medium ${
                tradeType === 'BUY'
                  ? 'bg-green-600 text-white'
                  : theme === 'dark'
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => setTradeType('BUY')}
            >
              Buy / Long
            </button>
            <button
              type="button"
              className={`flex-1 py-2 px-4 rounded-md font-medium ${
                tradeType === 'SELL'
                  ? 'bg-red-600 text-white'
                  : theme === 'dark'
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => setTradeType('SELL')}
            >
              Sell / Short
            </button>
          </div>
        </div>

        {/* Amount */}
        <div>
          <label htmlFor="amount" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Amount (USD)
          </label>
          <input
            id="amount"
            type="number"
            min="10"
            step="10"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
            required
          />
        </div>

        {/* Leverage */}
        <div>
          <label htmlFor="leverage" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Leverage ({leverage}x)
          </label>
          <input
            id="leverage"
            type="range"
            min="1"
            max="100"
            value={leverage}
            onChange={(e) => setLeverage(parseInt(e.target.value))}
            className="w-full"
          />
        </div>

        {/* Take profit */}
        <div>
          <label htmlFor="takeProfit" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Take Profit (optional)
          </label>
          <input
            id="takeProfit"
            type="number"
            step="0.01"
            value={takeProfit}
            onChange={(e) => setTakeProfit(e.target.value)}
            placeholder={asset.current_price ? `e.g. ${(parseFloat(asset.current_price) * 1.05).toFixed(2)}` : 'Enter price'}
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {/* Stop loss */}
        <div>
          <label htmlFor="stopLoss" className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Stop Loss (optional)
          </label>
          <input
            id="stopLoss"
            type="number"
            step="0.01"
            value={stopLoss}
            onChange={(e) => setStopLoss(e.target.value)}
            placeholder={asset.current_price ? `e.g. ${(parseFloat(asset.current_price) * 0.95).toFixed(2)}` : 'Enter price'}
            className={`w-full ${
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {/* Submit button */}
        <div className="md:col-span-2">
          <Button
            type="submit"
            variant={tradeType === 'BUY' ? 'primary' : 'danger'}
            className="w-full py-3"
            disabled={isSubmitting}
          >
            {isSubmitting
              ? 'Placing Order...'
              : `${tradeType === 'BUY' ? 'Buy' : 'Sell'} ${asset.symbol} at $${asset.current_price ? parseFloat(asset.current_price).toLocaleString() : 'N/A'}`
            }
          </Button>
        </div>
      </form>
    </div>
  );
}
