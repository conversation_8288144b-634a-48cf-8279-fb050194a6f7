'use client';

import { useState, useEffect, useRef } from 'react';
import { Asset } from '../../api/trading';

interface TradingChartProps {
  asset: Asset;
  priceHistory: any[];
  theme: string;
}

export default function TradingChart({ asset, priceHistory, theme }: TradingChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [timeframe, setTimeframe] = useState('1D');
  const [chartType, setChartType] = useState('candle');

  // Helper function to check if price change is positive
  const isPriceChangePositive = (change: string | number) => {
    const changeStr = String(change || '0');
    const changeNum = parseFloat(changeStr);
    return changeNum >= 0;
  };

  // Draw chart on canvas
  useEffect(() => {
    if (!canvasRef.current || priceHistory.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Chart settings
    const padding = { top: 20, right: 20, bottom: 30, left: 60 };
    const chartWidth = canvas.width - padding.left - padding.right;
    const chartHeight = canvas.height - padding.top - padding.bottom;

    // Get min and max prices
    const prices = priceHistory.map(point => parseFloat(point.price));
    const minPrice = Math.min(...prices) * 0.99;
    const maxPrice = Math.max(...prices) * 1.01;
    const priceRange = maxPrice - minPrice;

    // Set colors based on theme
    const axisColor = theme === 'dark' ? '#4B5563' : '#9CA3AF'; // gray-600 or gray-400
    const gridColor = theme === 'dark' ? '#374151' : '#E5E7EB'; // gray-700 or gray-200
    const textColor = theme === 'dark' ? '#9CA3AF' : '#6B7280'; // gray-400 or gray-500
    const lineColor = '#3B82F6'; // blue-500 for both themes

    // Draw price axis (y-axis)
    ctx.beginPath();
    ctx.strokeStyle = axisColor;
    ctx.lineWidth = 1;
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, padding.top + chartHeight);
    ctx.stroke();

    // Draw price labels
    ctx.font = '10px sans-serif';
    ctx.fillStyle = textColor;
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';

    const numPriceLabels = 5;
    for (let i = 0; i <= numPriceLabels; i++) {
      const y = padding.top + chartHeight - (i / numPriceLabels) * chartHeight;
      const price = minPrice + (i / numPriceLabels) * priceRange;

      ctx.fillText(price.toFixed(2), padding.left - 5, y);

      // Draw horizontal grid line
      ctx.beginPath();
      ctx.strokeStyle = gridColor;
      ctx.lineWidth = 0.5;
      ctx.moveTo(padding.left, y);
      ctx.lineTo(padding.left + chartWidth, y);
      ctx.stroke();
    }

    // Draw time axis (x-axis)
    ctx.beginPath();
    ctx.strokeStyle = axisColor;
    ctx.lineWidth = 1;
    ctx.moveTo(padding.left, padding.top + chartHeight);
    ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
    ctx.stroke();

    // Draw time labels
    ctx.font = '10px sans-serif';
    ctx.fillStyle = textColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';

    const numTimeLabels = Math.min(6, priceHistory.length);
    for (let i = 0; i < numTimeLabels; i++) {
      const x = padding.left + (i / (numTimeLabels - 1)) * chartWidth;
      const index = Math.floor((i / (numTimeLabels - 1)) * (priceHistory.length - 1));
      const date = new Date(priceHistory[index].timestamp);
      const label = date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });

      ctx.fillText(label, x, padding.top + chartHeight + 5);
    }

    // Draw price line
    ctx.beginPath();
    ctx.strokeStyle = lineColor;
    ctx.lineWidth = 2;

    for (let i = 0; i < priceHistory.length; i++) {
      const x = padding.left + (i / (priceHistory.length - 1)) * chartWidth;
      const price = parseFloat(priceHistory[i].price);
      const y = padding.top + chartHeight - ((price - minPrice) / priceRange) * chartHeight;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }

    ctx.stroke();

    // Add gradient under the line
    const gradient = ctx.createLinearGradient(0, padding.top, 0, padding.top + chartHeight);
    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.2)'); // blue-500 with alpha
    gradient.addColorStop(1, 'rgba(59, 130, 246, 0)');

    ctx.fillStyle = gradient;
    ctx.beginPath();

    // Start from the bottom left
    ctx.moveTo(padding.left, padding.top + chartHeight);

    // Draw the line again
    for (let i = 0; i < priceHistory.length; i++) {
      const x = padding.left + (i / (priceHistory.length - 1)) * chartWidth;
      const price = parseFloat(priceHistory[i].price);
      const y = padding.top + chartHeight - ((price - minPrice) / priceRange) * chartHeight;

      ctx.lineTo(x, y);
    }

    // Close the path to the bottom right
    ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
    ctx.closePath();
    ctx.fill();

  }, [asset, priceHistory, chartType, theme]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current) {
        canvasRef.current.width = canvasRef.current.offsetWidth;
        canvasRef.current.height = canvasRef.current.offsetHeight;
        // Redraw chart
        // This will trigger the useEffect above
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="h-full flex flex-col">
      {/* Chart header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div>
          <h2 className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            {asset.symbol}
          </h2>
          <div className="flex items-center mt-1">
            <span className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mr-2`}>
              ${parseFloat(asset.current_price).toLocaleString()}
            </span>
            <span className={`text-sm font-medium ${
              isPriceChangePositive(asset.price_change_24h)
                ? 'text-green-500'
                : 'text-red-500'
            }`}>
              {isPriceChangePositive(asset.price_change_24h) ? '+' : ''}
              {asset.price_change_percentage_24h}%
            </span>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {/* Timeframe selector */}
          <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'} rounded-lg p-1 flex`}>
            {['1H', '1D', '1W', '1M', '1Y'].map((tf) => (
              <button
                key={tf}
                className={`px-3 py-1 text-xs font-medium rounded ${
                  timeframe === tf
                    ? theme === 'dark'
                      ? 'bg-gray-700 text-white'
                      : 'bg-white text-gray-900'
                    : theme === 'dark'
                      ? 'text-gray-400 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setTimeframe(tf)}
              >
                {tf}
              </button>
            ))}
          </div>

          {/* Chart type selector */}
          <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'} rounded-lg p-1 flex`}>
            <button
              className={`px-3 py-1 text-xs font-medium rounded ${
                chartType === 'line'
                  ? theme === 'dark'
                    ? 'bg-gray-700 text-white'
                    : 'bg-white text-gray-900'
                  : theme === 'dark'
                    ? 'text-gray-400 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setChartType('line')}
            >
              Line
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium rounded ${
                chartType === 'candle'
                  ? theme === 'dark'
                    ? 'bg-gray-700 text-white'
                    : 'bg-white text-gray-900'
                  : theme === 'dark'
                    ? 'text-gray-400 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setChartType('candle')}
            >
              Candle
            </button>
          </div>
        </div>
      </div>

      {/* Chart canvas */}
      <div className="flex-1 relative min-h-[300px]">
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full"
        />
      </div>
    </div>
  );
}
