'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import TradingViewChart from './TradingViewChart';
import AssetList from './AssetList';
import ProfessionalOrderForm from './ProfessionalOrderForm';
import OrdersPanel from './OrdersPanel';
import PositionsPanel from './PositionsPanel';
import ExecutionsPanel from './ExecutionsPanel';
import MarketDataPanel from './MarketDataPanel';
import TradingViewWidgets from './TradingViewWidgets';
import { Asset, TradingAccount, TradingApiService } from '../../lib/api';
import { ProfessionalTradingApiService, Instrument } from '../../lib/api/professional-trading';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface TradingDashboardProps {
  className?: string;
}

export default function TradingDashboard({ className = '' }: TradingDashboardProps) {
  const { theme } = useTheme();
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // State management
  const [selectedAsset, setSelectedAsset] = useState<EnhancedAsset | null>(null);
  const [assets, setAssets] = useState<EnhancedAsset[]>([]);
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [accounts, setAccounts] = useState<TradingAccount[]>([]);
  const [activePanel, setActivePanel] = useState<'orders' | 'positions' | 'executions' | 'market'>('orders');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Layout state
  const [showLeftPanel, setShowLeftPanel] = useState(true);
  const [showRightPanel, setShowRightPanel] = useState(true);
  const [showBottomPanel, setShowBottomPanel] = useState(true);

  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      loadInitialData();
    }
  }, [authLoading, isAuthenticated]);

  const loadInitialData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load assets and accounts first (basic functionality)
      const [assetsResponse, accountsResponse] = await Promise.all([
        TradingApiService.getAssets(),
        TradingApiService.getTradingAccounts(),
      ]);

      if (assetsResponse.data?.results) {
        setAssets(assetsResponse.data.results);
        // Auto-select first asset
        if (assetsResponse.data.results.length > 0) {
          setSelectedAsset(assetsResponse.data.results[0] as EnhancedAsset);
        }
      }

      if (accountsResponse.data?.results) {
        setAccounts(accountsResponse.data.results);
      }

      // Try to load instruments separately (optional for professional features)
      try {
        const instrumentsResponse = await ProfessionalTradingApiService.getInstruments();
        if (instrumentsResponse.data?.results) {
          setInstruments(instrumentsResponse.data.results);
        }
      } catch (instrumentsError) {
        console.warn('Professional trading instruments not available:', instrumentsError);
        // Continue without instruments - not critical for basic functionality
      }

    } catch (err: any) {
      console.error('Error loading trading data:', err);
      setError(err.message || 'Failed to load trading data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssetSelect = (asset: Asset) => {
    setSelectedAsset(asset as EnhancedAsset);
  };

  if (authLoading || isLoading) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading trading dashboard...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-4`}>
            Authentication Required
          </h2>
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Please log in to access the trading dashboard.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className={`text-6xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
            ⚠️
          </div>
          <h2 className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-4`}>
            Trading Platform Error
          </h2>
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
            {error}
          </p>
          <button
            onClick={loadInitialData}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'} ${className}`}>
      {/* Header */}
      <div className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-4 py-3`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Professional Trading
            </h1>
            {selectedAsset && (
              <div className="flex items-center space-x-2">
                <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  Trading:
                </span>
                <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  {selectedAsset.symbol} - {selectedAsset.name}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              {accounts.length} Trading Account{accounts.length !== 1 ? 's' : ''}
            </span>
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              {assets.length} Assets Available
            </span>
          </div>
        </div>
      </div>

      {/* Main Trading Interface */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Left Panel - Asset List */}
        <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r flex flex-col`}>
          <AssetList
            assets={assets}
            selectedAsset={selectedAsset}
            onAssetSelect={handleAssetSelect}
            theme={theme}
          />
        </div>

        {/* Center Panel - Chart */}
        <div className="flex-1 flex flex-col">
          {/* Chart Area */}
          <div className="flex-1 p-2 lg:p-4">
            {selectedAsset ? (
              <TradingViewChart
                asset={selectedAsset}
                theme={theme}
              />
            ) : (
              <div className={`flex items-center justify-center h-full ${
                theme === 'dark' ? 'bg-gray-800' : 'bg-white'
              } rounded-lg`}>
                <div className="text-center">
                  <div className={`text-6xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                    📈
                  </div>
                  <p className={`text-lg ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    Select an asset to view chart
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Trading Form */}
        <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-l flex flex-col`}>
          <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
            <h2 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-2`}>
              Place Order
            </h2>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              {selectedAsset ? `Trade ${selectedAsset.symbol}` : 'Select an asset to trade'}
            </p>
          </div>

          <div className="flex-1 p-4">
            {selectedAsset && accounts.length > 0 ? (
              <ProfessionalOrderForm
                asset={selectedAsset}
                accounts={accounts}
                theme={theme}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                    {accounts.length === 0 ? '💳' : '🎯'}
                  </div>
                  <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    {accounts.length === 0
                      ? "Create a trading account to start trading"
                      : "Select an asset to place orders"}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
