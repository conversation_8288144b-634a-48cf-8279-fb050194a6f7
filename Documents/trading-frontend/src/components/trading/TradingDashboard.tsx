'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import TradingViewChart from './TradingViewChart';
import AssetList from './AssetList';
import ProfessionalOrderForm from './ProfessionalOrderForm';
import OrdersPanel from './OrdersPanel';
import PositionsPanel from './PositionsPanel';
import ExecutionsPanel from './ExecutionsPanel';
import MarketDataPanel from './MarketDataPanel';
import TradingViewWidgets from './TradingViewWidgets';
import TradingSystemStatus from './TradingSystemStatus';
import { Asset, TradingAccount, TradingApiService } from '../../lib/api';
import { ProfessionalTradingApiService, Instrument } from '../../lib/api/professional-trading';
import { finnhubApi } from '../../lib/api/finnhub';
import FinnhubApiService from '../../lib/api/finnhub';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface TradingDashboardProps {
  className?: string;
}

export default function TradingDashboard({ className = '' }: TradingDashboardProps) {
  const { theme } = useTheme();
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // State management
  const [selectedAsset, setSelectedAsset] = useState<EnhancedAsset | null>(null);
  const [assets, setAssets] = useState<EnhancedAsset[]>([]);
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [accounts, setAccounts] = useState<TradingAccount[]>([]);
  const [activePanel, setActivePanel] = useState<'orders' | 'positions' | 'executions' | 'market'>('orders');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Layout state
  const [showLeftPanel, setShowLeftPanel] = useState(true);
  const [showRightPanel, setShowRightPanel] = useState(true);
  const [showBottomPanel, setShowBottomPanel] = useState(true);

  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      loadInitialData();
    }
  }, [authLoading, isAuthenticated]);

  const loadInitialData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load assets and accounts first (basic functionality)
      const [assetsResponse, accountsResponse] = await Promise.all([
        TradingApiService.getAssets(),
        TradingApiService.getTradingAccounts(),
      ]);

      if (assetsResponse.data?.results) {
        setAssets(assetsResponse.data.results);
        // Auto-select first asset
        if (assetsResponse.data.results.length > 0) {
          setSelectedAsset(assetsResponse.data.results[0] as EnhancedAsset);
        }
      }

      if (accountsResponse.data?.results) {
        setAccounts(accountsResponse.data.results);
      }

      // Try to load instruments separately (optional for professional features)
      try {
        const instrumentsResponse = await ProfessionalTradingApiService.getInstruments();
        if (instrumentsResponse.data?.results) {
          setInstruments(instrumentsResponse.data.results);
        }
      } catch (instrumentsError) {
        console.warn('Professional trading instruments not available:', instrumentsError);
        // Continue without instruments - not critical for basic functionality
      }

      // Load real-time prices for assets using Finnhub
      if (assetsResponse.data?.results) {
        loadRealTimePrices(assetsResponse.data.results);
      }

    } catch (err: any) {
      console.error('Error loading trading data:', err);
      setError(err.message || 'Failed to load trading data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadRealTimePrices = async (assetList: Asset[]) => {
    try {
      // Filter assets that should use Finnhub (not crypto)
      const finnhubAssets = assetList.filter(asset =>
        FinnhubApiService.shouldUseFinnhub(asset.asset_type)
      );

      if (finnhubAssets.length === 0) return;

      // Get symbols for Finnhub
      const symbols = finnhubAssets.map(asset => asset.symbol);

      // Load quotes from Finnhub
      const quotes = await finnhubApi.getMultipleQuotes(symbols);

      // Update assets with real-time prices
      const updatedAssets = assetList.map(asset => {
        const quote = quotes[asset.symbol];
        if (quote && FinnhubApiService.shouldUseFinnhub(asset.asset_type)) {
          const standardFormat = FinnhubApiService.convertToStandardFormat(quote, asset.symbol);
          return {
            ...asset,
            current_price: standardFormat.current_price,
            price_change_24h: standardFormat.price_change_24h,
            price_change_percentage_24h: standardFormat.price_change_percentage_24h,
            last_updated: standardFormat.last_updated,
          } as EnhancedAsset;
        }
        return asset as EnhancedAsset;
      });

      setAssets(updatedAssets);

      // Update selected asset if it has new price data
      if (selectedAsset) {
        const updatedSelected = updatedAssets.find(a => a.id === selectedAsset.id);
        if (updatedSelected) {
          setSelectedAsset(updatedSelected);
        }
      }

    } catch (error) {
      console.error('Error loading real-time prices:', error);
    }
  };

  const handleAssetSelect = (asset: Asset) => {
    const enhancedAsset = asset as EnhancedAsset;
    setSelectedAsset(enhancedAsset);

    // Load fresh price data for the selected asset using Finnhub
    if (FinnhubApiService.shouldUseFinnhub(asset.asset_type)) {
      finnhubApi.getQuote(asset.symbol).then((quote: any) => {
        if (quote) {
          const standardFormat = FinnhubApiService.convertToStandardFormat(quote, asset.symbol);
          setSelectedAsset({
            ...enhancedAsset,
            current_price: standardFormat.current_price,
            price_change_24h: standardFormat.price_change_24h,
            price_change_percentage_24h: standardFormat.price_change_percentage_24h,
            last_updated: standardFormat.last_updated,
          });
        }
      }).catch((error: any) => {
        console.warn('Error loading fresh price for selected asset:', error);
      });
    }
  };

  if (authLoading || isLoading) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Loading trading dashboard...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-4`}>
            Authentication Required
          </h2>
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Please log in to access the trading dashboard.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className={`text-6xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
            ⚠️
          </div>
          <h2 className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-4`}>
            Trading Platform Error
          </h2>
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
            {error}
          </p>
          <button
            onClick={loadInitialData}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'} ${className}`}>
      {/* Header */}
      <div className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-4 py-3`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Professional Trading
            </h1>
            {selectedAsset && (
              <div className="flex items-center space-x-2">
                <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  Trading:
                </span>
                <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  {selectedAsset.symbol} - {selectedAsset.name}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              {accounts.length} Trading Account{accounts.length !== 1 ? 's' : ''}
            </span>
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              {assets.length} Assets Available
            </span>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="p-4">
        <TradingSystemStatus theme={theme} />
      </div>

      {/* Main Trading Interface */}
      <div className="flex h-[calc(100vh-200px)]">
        {/* Left Panel - Asset List */}
        <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r flex flex-col`}>
          <AssetList
            assets={assets}
            selectedAsset={selectedAsset}
            onAssetSelect={handleAssetSelect}
            theme={theme}
          />
        </div>

        {/* Center Panel - Chart */}
        <div className="flex-1 flex flex-col">
          {/* Chart Area */}
          <div className="flex-1 p-2 lg:p-4">
            {selectedAsset ? (
              <div className="h-full flex flex-col space-y-4">
                {/* Main Chart */}
                <div className="flex-1">
                  <TradingViewChart
                    asset={selectedAsset}
                    theme={theme}
                  />
                </div>

                {/* TradingView Widgets */}
                <div className="h-48">
                  <TradingViewWidgets
                    symbol={selectedAsset.symbol}
                    theme={theme}
                  />
                </div>
              </div>
            ) : (
              <div className={`flex items-center justify-center h-full ${
                theme === 'dark' ? 'bg-gray-800' : 'bg-white'
              } rounded-lg`}>
                <div className="text-center">
                  <div className={`text-6xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                    📈
                  </div>
                  <p className={`text-lg ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    Select an asset to view professional trading interface
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Trading Form */}
        <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-l flex flex-col`}>
          <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
            <h2 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-2`}>
              Place Order
            </h2>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              {selectedAsset ? `Trade ${selectedAsset.symbol}` : 'Select an asset to trade'}
            </p>
          </div>

          <div className="flex-1 p-4">
            {selectedAsset && accounts.length > 0 ? (
              <ProfessionalOrderForm
                asset={selectedAsset}
                accounts={accounts}
                theme={theme}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                    {accounts.length === 0 ? '💳' : '🎯'}
                  </div>
                  <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    {accounts.length === 0
                      ? "Create a trading account to start trading"
                      : "Select an asset to place orders"}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Panel - Trading Activities */}
      {showBottomPanel && (
        <div className={`h-80 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-t flex flex-col`}>
          {/* Panel Tabs */}
          <div className={`flex border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
            {[
              { key: 'orders', label: 'Orders', icon: '📋' },
              { key: 'positions', label: 'Positions', icon: '📊' },
              { key: 'executions', label: 'Executions', icon: '⚡' },
              { key: 'market', label: 'Market Data', icon: '📈' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActivePanel(tab.key as any)}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activePanel === tab.key
                    ? theme === 'dark'
                      ? 'border-blue-500 text-blue-400 bg-gray-700'
                      : 'border-blue-500 text-blue-600 bg-blue-50'
                    : theme === 'dark'
                      ? 'border-transparent text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>

          {/* Panel Content */}
          <div className="flex-1 overflow-hidden">
            {activePanel === 'orders' && <OrdersPanel theme={theme} />}
            {activePanel === 'positions' && <PositionsPanel theme={theme} />}
            {activePanel === 'executions' && <ExecutionsPanel theme={theme} />}
            {activePanel === 'market' && <MarketDataPanel theme={theme} />}
          </div>
        </div>
      )}

      {/* Panel Toggle Button */}
      <div className="fixed bottom-4 right-4">
        <button
          onClick={() => setShowBottomPanel(!showBottomPanel)}
          className={`p-3 rounded-full shadow-lg transition-colors ${
            theme === 'dark'
              ? 'bg-gray-700 hover:bg-gray-600 text-white'
              : 'bg-white hover:bg-gray-50 text-gray-900'
          } border ${theme === 'dark' ? 'border-gray-600' : 'border-gray-200'}`}
          title={showBottomPanel ? 'Hide Trading Panel' : 'Show Trading Panel'}
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="fixed bottom-4 left-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg max-w-md">
          <div className="flex items-center space-x-2">
            <span>⚠️</span>
            <span className="text-sm">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-2 text-white hover:text-gray-200"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
