'use client';

import { useEffect, useRef, useState } from 'react';
import { Asset } from '../../lib/api';
import LightweightChart from './LightweightChart';

// Enhanced asset type with live price data
type EnhancedAsset = Asset & {
  current_price?: string;
  price_change_24h?: string;
  price_change_percentage_24h?: string;
  last_updated?: string;
};

interface TradingViewChartProps {
  asset: EnhancedAsset;
  theme: string;
}

declare global {
  interface Window {
    TradingView: any;
  }
}

export default function TradingViewChart({ asset, theme }: TradingViewChartProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const widgetRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useFallback, setUseFallback] = useState(false);

  // Map asset symbols to TradingView format
  const getTradingViewSymbol = (asset: EnhancedAsset) => {
    const symbol = asset.symbol.toUpperCase();
    
    // Handle different asset types
    switch (asset.asset_type) {
      case 'CRYPTO':
        // Convert BTC/USD to BINANCE:BTCUSDT format
        if (symbol.includes('/')) {
          const [base, quote] = symbol.split('/');
          return `BINANCE:${base}${quote}T`;
        }
        return `BINANCE:${symbol}USDT`;
      
      case 'STOCK':
        // Handle stock symbols
        return `NASDAQ:${symbol}`;
      
      case 'FOREX':
        // Convert EUR/USD to FX:EURUSD format
        if (symbol.includes('/')) {
          const [base, quote] = symbol.split('/');
          return `FX:${base}${quote}`;
        }
        return `FX:${symbol}`;
      
      case 'COMMODITY':
        // Handle commodities
        if (symbol === 'GOLD') return 'TVC:GOLD';
        if (symbol === 'SILVER') return 'TVC:SILVER';
        if (symbol === 'OIL') return 'TVC:USOIL';
        return `TVC:${symbol}`;
      
      default:
        return `NASDAQ:${symbol}`;
    }
  };

  useEffect(() => {
    if (!containerRef.current) return;

    // Clean up previous widget
    if (widgetRef.current) {
      try {
        widgetRef.current.remove();
      } catch (e) {
        console.warn('Error removing TradingView widget:', e);
      }
      widgetRef.current = null;
    }

    setIsLoading(true);
    setError(null);

    // Load TradingView script if not already loaded
    const loadTradingViewScript = () => {
      return new Promise<void>((resolve, reject) => {
        if (window.TradingView) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://s3.tradingview.com/tv.js';
        script.async = true;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load TradingView script'));
        document.head.appendChild(script);
      });
    };

    const initializeWidget = async () => {
      try {
        await loadTradingViewScript();

        if (!containerRef.current) return;

        const tradingViewSymbol = getTradingViewSymbol(asset);
        
        widgetRef.current = new window.TradingView.widget({
          autosize: true,
          symbol: tradingViewSymbol,
          interval: '5',
          container_id: containerRef.current.id,
          library_path: '/charting_library/',
          locale: 'en',
          disabled_features: [
            'use_localstorage_for_settings',
            'volume_force_overlay',
            'header_compare',
            'header_undo_redo',
            'header_screenshot',
            'header_saveload'
          ],
          enabled_features: [
            'study_templates',
            'side_toolbar_in_fullscreen_mode'
          ],
          charts_storage_url: 'https://saveload.tradingview.com',
          charts_storage_api_version: '1.1',
          client_id: 'tradingview.com',
          user_id: 'public_user_id',
          fullscreen: false,
          theme: theme === 'dark' ? 'dark' : 'light',
          style: '1', // Candle style
          toolbar_bg: theme === 'dark' ? '#1f2937' : '#f9fafb',
          overrides: {
            'paneProperties.background': theme === 'dark' ? '#1f2937' : '#ffffff',
            'paneProperties.vertGridProperties.color': theme === 'dark' ? '#374151' : '#e5e7eb',
            'paneProperties.horzGridProperties.color': theme === 'dark' ? '#374151' : '#e5e7eb',
            'symbolWatermarkProperties.transparency': 90,
            'scalesProperties.textColor': theme === 'dark' ? '#d1d5db' : '#374151',
          },
          studies_overrides: {},
          time_frames: [
            { text: '1m', resolution: '1', description: '1 Minute' },
            { text: '5m', resolution: '5', description: '5 Minutes' },
            { text: '15m', resolution: '15', description: '15 Minutes' },
            { text: '1h', resolution: '60', description: '1 Hour' },
            { text: '4h', resolution: '240', description: '4 Hours' },
            { text: '1D', resolution: '1D', description: '1 Day' },
            { text: '1W', resolution: '1W', description: '1 Week' },
          ],
        });

        setIsLoading(false);
      } catch (err) {
        console.error('Error initializing TradingView widget:', err);
        console.log('Falling back to lightweight chart...');
        setUseFallback(true);
        setError(null);
        setIsLoading(false);
      }
    };

    initializeWidget();

    return () => {
      if (widgetRef.current) {
        try {
          widgetRef.current.remove();
        } catch (e) {
          console.warn('Error cleaning up TradingView widget:', e);
        }
        widgetRef.current = null;
      }
    };
  }, [asset, theme]);

  const containerId = `tradingview_${asset.id}_${Date.now()}`;

  // Use fallback chart if TradingView failed to load
  if (useFallback) {
    return (
      <LightweightChart
        asset={asset}
        theme={theme}
      />
    );
  }

  if (error) {
    return (
      <div className={`h-full flex items-center justify-center ${
        theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'
      }`}>
        <div className="text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className="text-sm">{error}</p>
          <div className="mt-2 space-x-2">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
            >
              Retry
            </button>
            <button
              onClick={() => setUseFallback(true)}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm"
            >
              Use Simple Chart
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full relative">
      {isLoading && (
        <div className={`absolute inset-0 flex items-center justify-center z-10 ${
          theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'
        }`}>
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>
              Loading chart...
            </span>
          </div>
        </div>
      )}
      
      <div
        ref={containerRef}
        id={containerId}
        className="h-full w-full"
        style={{ minHeight: '400px' }}
      />
    </div>
  );
}
