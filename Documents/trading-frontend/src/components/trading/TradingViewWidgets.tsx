'use client';

import { useEffect, useRef, useState } from 'react';

interface TradingViewWidgetsProps {
  symbol: string;
  theme: string;
}

declare global {
  interface Window {
    TradingView: any;
  }
}

export default function TradingViewWidgets({ symbol, theme }: TradingViewWidgetsProps) {
  const marketOverviewRef = useRef<HTMLDivElement>(null);
  const screenerRef = useRef<HTMLDivElement>(null);
  const tickerRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [activeWidget, setActiveWidget] = useState<'overview' | 'screener' | 'ticker'>('overview');

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/tv.js';
    script.async = true;
    script.onload = () => {
      setIsLoaded(true);
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts
      const existingScript = document.querySelector('script[src="https://s3.tradingview.com/tv.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  useEffect(() => {
    if (isLoaded && window.TradingView) {
      initializeWidgets();
    }
  }, [isLoaded, symbol, theme, activeWidget]);

  const initializeWidgets = () => {
    if (!window.TradingView) return;

    // Clear existing widgets
    if (marketOverviewRef.current) {
      marketOverviewRef.current.innerHTML = '';
    }
    if (screenerRef.current) {
      screenerRef.current.innerHTML = '';
    }
    if (tickerRef.current) {
      tickerRef.current.innerHTML = '';
    }

    const widgetTheme = theme === 'dark' ? 'dark' : 'light';

    // Market Overview Widget
    if (activeWidget === 'overview' && marketOverviewRef.current) {
      new window.TradingView.widget({
        container_id: marketOverviewRef.current.id,
        width: '100%',
        height: '100%',
        symbol: getFormattedSymbol(symbol),
        interval: '1D',
        timezone: 'Etc/UTC',
        theme: widgetTheme,
        style: '1',
        locale: 'en',
        toolbar_bg: theme === 'dark' ? '#1f2937' : '#ffffff',
        enable_publishing: false,
        hide_top_toolbar: true,
        hide_legend: true,
        save_image: false,
        studies: [
          'MASimple@tv-basicstudies',
          'RSI@tv-basicstudies'
        ],
        show_popup_button: true,
        popup_width: '1000',
        popup_height: '650'
      });
    }

    // Stock Screener Widget
    if (activeWidget === 'screener' && screenerRef.current) {
      new window.TradingView.MediumWidget({
        container_id: screenerRef.current.id,
        symbols: [
          ['NASDAQ:AAPL', 'Apple'],
          ['NASDAQ:GOOGL', 'Google'],
          ['NASDAQ:MSFT', 'Microsoft'],
          ['NASDAQ:AMZN', 'Amazon'],
          ['NASDAQ:TSLA', 'Tesla'],
          ['NYSE:JPM', 'JPMorgan'],
        ],
        chartOnly: false,
        width: '100%',
        height: '100%',
        locale: 'en',
        colorTheme: widgetTheme,
        autosize: true,
        showVolume: false,
        hideDateRanges: false,
        scalePosition: 'right',
        scaleMode: 'Normal',
        fontFamily: '-apple-system, BlinkMacSystemFont, Trebuchet MS, Roboto, Ubuntu, sans-serif',
        noTimeScale: false,
        valuesTracking: '1',
        chartType: 'line'
      });
    }

    // Ticker Widget
    if (activeWidget === 'ticker' && tickerRef.current) {
      new window.TradingView.widget({
        container_id: tickerRef.current.id,
        width: '100%',
        height: '100%',
        symbol: getFormattedSymbol(symbol),
        interval: '1',
        timezone: 'Etc/UTC',
        theme: widgetTheme,
        style: '1',
        locale: 'en',
        toolbar_bg: theme === 'dark' ? '#1f2937' : '#ffffff',
        enable_publishing: false,
        hide_top_toolbar: true,
        hide_legend: true,
        save_image: false,
        details: true,
        hotlist: true,
        calendar: false,
        studies: [],
        show_popup_button: false
      });
    }
  };

  const getFormattedSymbol = (symbol: string) => {
    // Map common symbols to TradingView format
    const symbolMap: { [key: string]: string } = {
      'AAPL': 'NASDAQ:AAPL',
      'GOOGL': 'NASDAQ:GOOGL',
      'MSFT': 'NASDAQ:MSFT',
      'AMZN': 'NASDAQ:AMZN',
      'TSLA': 'NASDAQ:TSLA',
      'JPM': 'NYSE:JPM',
      'BTC': 'BINANCE:BTCUSDT',
      'ETH': 'BINANCE:ETHUSDT',
      'EUR/USD': 'FX:EURUSD',
      'GBP/USD': 'FX:GBPUSD',
      'USD/JPY': 'FX:USDJPY',
      'GOLD': 'TVC:GOLD',
      'SILVER': 'TVC:SILVER',
      'OIL': 'TVC:USOIL',
    };

    return symbolMap[symbol] || `NASDAQ:${symbol}`;
  };

  const widgets = [
    { key: 'overview', label: 'Market Overview', icon: '📊' },
    { key: 'screener', label: 'Stock Screener', icon: '🔍' },
    { key: 'ticker', label: 'Live Ticker', icon: '📈' },
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Widget Tabs */}
      <div className={`flex border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        {widgets.map((widget) => (
          <button
            key={widget.key}
            onClick={() => setActiveWidget(widget.key as any)}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeWidget === widget.key
                ? theme === 'dark'
                  ? 'border-blue-500 text-blue-400 bg-gray-700'
                  : 'border-blue-500 text-blue-600 bg-blue-50'
                : theme === 'dark'
                  ? 'border-transparent text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <span className="mr-2">{widget.icon}</span>
            {widget.label}
          </button>
        ))}
      </div>

      {/* Widget Content */}
      <div className="flex-1 relative">
        {!isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
              <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                Loading TradingView widgets...
              </p>
            </div>
          </div>
        )}

        {/* Market Overview Widget */}
        <div
          ref={marketOverviewRef}
          id="tradingview-market-overview"
          className={`w-full h-full ${activeWidget === 'overview' ? 'block' : 'hidden'}`}
        />

        {/* Stock Screener Widget */}
        <div
          ref={screenerRef}
          id="tradingview-screener"
          className={`w-full h-full ${activeWidget === 'screener' ? 'block' : 'hidden'}`}
        />

        {/* Ticker Widget */}
        <div
          ref={tickerRef}
          id="tradingview-ticker"
          className={`w-full h-full ${activeWidget === 'ticker' ? 'block' : 'hidden'}`}
        />

        {/* Fallback Content */}
        {!isLoaded && (
          <div className={`w-full h-full flex items-center justify-center ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'
          } rounded-lg`}>
            <div className="text-center">
              <div className={`text-4xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-400'}`}>
                📊
              </div>
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                TradingView widgets loading...
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Widget Info */}
      <div className={`px-4 py-2 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between text-xs">
          <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
            {activeWidget === 'overview' && 'Advanced charting with technical indicators'}
            {activeWidget === 'screener' && 'Real-time market screening and analysis'}
            {activeWidget === 'ticker' && 'Live price ticker and market data'}
          </span>
          <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>
            Powered by TradingView
          </span>
        </div>
      </div>
    </div>
  );
}
