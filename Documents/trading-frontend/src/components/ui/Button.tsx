'use client';

import React from 'react';
import Link from 'next/link';
import { useTheme } from '../../context/ThemeContext';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface ButtonProps {
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  href?: string;
  fullWidth?: boolean;
  disabled?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
}

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  href,
  fullWidth = false,
  disabled = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  onClick,
  type = 'button',
}: ButtonProps) {
  const { theme } = useTheme();

  // Base classes for all button variants
  const baseClasses = `
    inline-flex items-center justify-center
    font-medium rounded-lg
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-2
    ${fullWidth ? 'w-full' : ''}
    ${disabled || isLoading ? 'opacity-60 cursor-not-allowed' : 'transform hover:-translate-y-0.5 active:translate-y-0'}
  `;

  // Size classes
  const sizeClasses = {
    xs: 'text-xs px-2.5 py-1.5',
    sm: 'text-sm px-3 py-2',
    md: 'text-sm px-4 py-2',
    lg: 'text-base px-5 py-2.5',
    xl: 'text-lg px-6 py-3',
  };

  // Variant classes
  const variantClasses = {
    primary: `
      bg-blue-600 hover:bg-blue-700 
      dark:bg-blue-500 dark:hover:bg-blue-600 
      text-white 
      focus:ring-blue-500 dark:focus:ring-blue-400
      shadow-sm hover:shadow
    `,
    secondary: `
      bg-gray-100 hover:bg-gray-200 
      dark:bg-gray-700 dark:hover:bg-gray-600 
      text-gray-800 dark:text-gray-100
      focus:ring-gray-500 dark:focus:ring-gray-400
      shadow-sm hover:shadow
    `,
    outline: `
      bg-transparent 
      border border-gray-300 dark:border-gray-600
      hover:bg-gray-50 dark:hover:bg-gray-800
      text-gray-700 dark:text-gray-200
      focus:ring-gray-500 dark:focus:ring-gray-400
    `,
    ghost: `
      bg-transparent 
      hover:bg-gray-100 dark:hover:bg-gray-800
      text-gray-700 dark:text-gray-200
      focus:ring-gray-500 dark:focus:ring-gray-400
    `,
    danger: `
      bg-red-600 hover:bg-red-700 
      dark:bg-red-500 dark:hover:bg-red-600 
      text-white 
      focus:ring-red-500 dark:focus:ring-red-400
      shadow-sm hover:shadow
    `,
  };

  // Combine all classes
  const buttonClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `;

  // Loading spinner
  const LoadingSpinner = () => (
    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  );

  // Button content
  const buttonContent = (
    <>
      {isLoading && <LoadingSpinner />}
      {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      <span>{children}</span>
      {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </>
  );

  // Render as link if href is provided
  if (href) {
    return (
      <Link href={href} className={buttonClasses}>
        {buttonContent}
      </Link>
    );
  }

  // Render as button
  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled || isLoading}
    >
      {buttonContent}
    </button>
  );
}
