'use client';

import React, { useState } from 'react';
import { useTheme } from '../../context/ThemeContext';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  footer?: React.ReactNode;
  noPadding?: boolean;
  hoverable?: boolean;
  onClick?: () => void;
}

export default function Card({
  children,
  className = '',
  title,
  subtitle,
  icon,
  footer,
  noPadding = false,
  hoverable = false,
  onClick,
}: CardProps) {
  const { theme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    if (hoverable) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    if (hoverable) {
      setIsHovered(false);
    }
  };

  return (
    <div
      className={`
        bg-white dark:bg-gray-800
        rounded-xl
        overflow-hidden
        transition-all duration-300 ease-in-out
        animate-fade-in
        border border-gray-200 dark:border-gray-700
        ${theme === 'dark'
          ? isHovered
            ? 'shadow-card-hover-dark'
            : 'shadow-card-dark'
          : isHovered
            ? 'shadow-card-hover'
            : 'shadow-card'
        }
        ${hoverable ? 'cursor-pointer transform hover:-translate-y-1' : ''}
        ${className}
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
    >
      {/* Card Header */}
      {(title || icon) && (
        <div className="px-4 sm:px-6 py-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center">
            {icon && (
              <div className="mr-3 text-blue-500 dark:text-blue-400">
                {icon}
              </div>
            )}
            <div className="flex-1 min-w-0">
              {title && (
                <h3 className="text-base sm:text-lg font-heading font-semibold text-gray-900 dark:text-white truncate">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1 truncate">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Card Content */}
      <div className={noPadding ? '' : 'p-4 sm:p-6'}>
        {children}
      </div>

      {/* Card Footer */}
      {footer && (
        <div className="px-4 sm:px-6 py-3 bg-gray-50 dark:bg-gray-750 border-t border-gray-100 dark:border-gray-700">
          {footer}
        </div>
      )}
    </div>
  );
}
