'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { ColorMode, ThemeColors, getThemeColors } from '../theme/colors';

interface ThemeContextType {
  theme: ColorMode;
  themeColors: ThemeColors;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  themeColors: getThemeColors('light'),
  toggleTheme: () => {},
});

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize with a function to get the correct initial state
  const [theme, setTheme] = useState<ColorMode>(() => {
    // This will only run on the client side
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') as ColorMode | null;
      if (savedTheme) {
        return savedTheme;
      }

      // Check system preference
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
      }
    }

    return 'light';
  });

  const [themeColors, setThemeColors] = useState<ThemeColors>(() => getThemeColors(
    typeof window !== 'undefined' && localStorage.getItem('theme') === 'dark' ? 'dark' : 'light'
  ));

  useEffect(() => {
    // This function needs to run on the client side
    const initializeTheme = () => {
      // Check if theme is stored in localStorage
      const storedTheme = localStorage.getItem('theme') as ColorMode | null;

      // Check if user prefers dark mode
      const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

      // Set theme based on localStorage or user preference
      if (storedTheme && storedTheme !== theme) {
        // Only update if the stored theme is different from current theme
        setTheme(storedTheme);
      } else if (!storedTheme && prefersDarkMode && theme !== 'dark') {
        // Only update if no stored theme, user prefers dark, and current theme is not dark
        setTheme('dark');
      }

      // Apply theme class immediately to prevent flash (regardless of state update)
      const finalTheme = storedTheme || (prefersDarkMode ? 'dark' : 'light');
      if (finalTheme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    };

    // Run initialization
    initializeTheme();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    // Update document class when theme changes
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Update theme colors
    setThemeColors(getThemeColors(theme));

    // Store theme in localStorage
    localStorage.setItem('theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  return (
    <ThemeContext.Provider value={{ theme, themeColors, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
