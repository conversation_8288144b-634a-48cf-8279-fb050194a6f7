import { SecureSessionStorage } from './encryption';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Session storage keys
const SESSION_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  REFRESH_TOKEN: 'refresh_token',
} as const;

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  status: number;
}

// Django paginated response interface
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  account_type: string;
  is_verified: boolean;
}

export interface LoginResponse {
  token: string;
  user: User;
}

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Main API Client class
 */
export class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Get authentication token from secure session storage
   */
  private getAuthToken(): string | null {
    return SecureSessionStorage.getItem(SESSION_KEYS.AUTH_TOKEN);
  }

  /**
   * Set authentication token in secure session storage
   */
  private setAuthToken(token: string): void {
    SecureSessionStorage.setItem(SESSION_KEYS.AUTH_TOKEN, token);
  }

  /**
   * Remove authentication token from session storage
   */
  private removeAuthToken(): void {
    SecureSessionStorage.removeItem(SESSION_KEYS.AUTH_TOKEN);
  }

  /**
   * Get user data from secure session storage
   */
  getUserData(): User | null {
    return SecureSessionStorage.getItem(SESSION_KEYS.USER_DATA);
  }

  /**
   * Set user data in secure session storage
   */
  private setUserData(user: User): void {
    SecureSessionStorage.setItem(SESSION_KEYS.USER_DATA, user);
  }

  /**
   * Remove user data from session storage
   */
  private removeUserData(): void {
    SecureSessionStorage.removeItem(SESSION_KEYS.USER_DATA);
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getAuthToken();
    const user = this.getUserData();
    return !!(token && user);
  }

  /**
   * Make HTTP request with proper error handling
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getAuthToken();

    // Default headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Token ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const status = response.status;
      let data: any;

      // Get response text first, then try to parse as JSON
      const responseText = await response.text();

      try {
        data = responseText ? JSON.parse(responseText) : null;
      } catch {
        data = responseText;
      }

      if (!response.ok) {
        // Handle authentication errors
        if (status === 401) {
          this.logout();
          throw new ApiError('Authentication required', status, data);
        }

        // Handle other errors
        const errorMessage = data?.error || data?.message || `HTTP ${status}`;
        throw new ApiError(errorMessage, status, data);
      }

      return {
        data,
        status,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0,
        error
      );
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let url = endpoint;
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      url += `?${searchParams.toString()}`;
    }

    return this.makeRequest<T>(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE' });
  }

  /**
   * Upload file (multipart/form-data)
   */
  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getAuthToken();

    const headers: HeadersInit = {};

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Token ${token}`;
    }
    // Don't set Content-Type for FormData, let browser set it

    try {
      const response = await fetch(url, {
        method: 'PATCH',
        headers,
        body: formData,
      });

      const status = response.status;
      let data: any;

      // Get response text first, then try to parse as JSON
      const responseText = await response.text();

      try {
        data = responseText ? JSON.parse(responseText) : null;
      } catch {
        data = responseText;
      }

      if (!response.ok) {
        // Handle authentication errors
        if (status === 401) {
          this.logout();
          throw new ApiError('Authentication required', status, data);
        }

        // Handle other errors
        const errorMessage = data?.error || data?.message || `HTTP ${status}`;
        throw new ApiError(errorMessage, status, data);
      }

      return {
        data,
        status,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0,
        error
      );
    }
  }

  /**
   * Login user
   */
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    const response = await this.post<LoginResponse>('/auth/login/', {
      email,
      password,
    });

    if (response.data) {
      this.setAuthToken(response.data.token);
      this.setUserData(response.data.user);
    }

    return response;
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Call logout endpoint if authenticated
      if (this.isAuthenticated()) {
        await this.post('/auth/logout/');
      }
    } catch (error) {
      // Ignore logout errors
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local session data
      this.removeAuthToken();
      this.removeUserData();
      SecureSessionStorage.clear();
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await this.get<User>('/auth/user/');

    if (response.data) {
      this.setUserData(response.data);
    }

    return response;
  }

  /**
   * Register new user
   */
  async register(userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>('/auth/register/', userData);
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>('/auth/password-reset-request/', { email });
  }

  /**
   * Confirm password reset
   */
  async confirmPasswordReset(data: {
    token: string;
    new_password: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>('/auth/password-reset-confirm/', data);
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
