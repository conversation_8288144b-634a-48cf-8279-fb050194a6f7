import { apiClient, ApiResponse, PaginatedResponse } from '../api-client';

// Activity types
export interface Activity {
  id: string;
  actor: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  target_user?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  } | null;
  activity_type: string;
  description: string;
  created_at: string;
  ip_address?: string | null;
  action?: string;
}

/**
 * Activities API Service
 */
export class ActivitiesApiService {
  /**
   * Get user activities
   */
  static async getUserActivities(params?: {
    activity_type?: string;
    limit?: number;
    page?: number;
  }): Promise<ApiResponse<PaginatedResponse<Activity>>> {
    return apiClient.get<PaginatedResponse<Activity>>('/activities/', params);
  }

  /**
   * Get activity details
   */
  static async getActivity(activityId: string): Promise<ApiResponse<Activity>> {
    return apiClient.get<Activity>(`/activities/${activityId}/`);
  }
}

export default ActivitiesApiService;
