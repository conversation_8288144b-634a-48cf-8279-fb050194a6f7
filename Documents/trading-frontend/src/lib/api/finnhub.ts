/**
 * Finnhub.io API Service for Real-time Stock Prices
 * Used for all assets except cryptocurrency
 * API Key: d1h62jhr01qkdlvs22a0d1h62jhr01qkdlvs22ag
 */

export interface FinnhubQuote {
  c: number; // Current price
  d: number; // Change
  dp: number; // Percent change
  h: number; // High price of the day
  l: number; // Low price of the day
  o: number; // Open price of the day
  pc: number; // Previous close price
  t: number; // Timestamp
}

export interface FinnhubCandle {
  c: number[]; // Close prices
  h: number[]; // High prices
  l: number[]; // Low prices
  o: number[]; // Open prices
  s: string; // Status
  t: number[]; // Timestamps
  v: number[]; // Volume
}

export interface FinnhubSymbolLookup {
  count: number;
  result: Array<{
    description: string;
    displaySymbol: string;
    symbol: string;
    type: string;
  }>;
}

class FinnhubApiService {
  private baseUrl = 'https://finnhub.io/api/v1';
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_FINNHUB_API_KEY || 'd1h62jhr01qkdlvs22a0d1h62jhr01qkdlvs22ag';
  }

  /**
   * Get real-time quote for a symbol
   */
  async getQuote(symbol: string): Promise<FinnhubQuote | null> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response = await fetch(
        `${this.baseUrl}/quote?symbol=${formattedSymbol}&token=${this.apiKey}`
      );

      if (!response.ok) {
        throw new Error(`Finnhub API error: ${response.status}`);
      }

      const data = await response.json();
      
      // Check if we got valid data
      if (data.c === 0 && data.d === 0 && data.dp === 0) {
        console.warn(`No data available for symbol: ${symbol}`);
        return null;
      }

      return data;
    } catch (error) {
      console.error(`Error fetching quote for ${symbol}:`, error);
      return null;
    }
  }

  /**
   * Get multiple quotes at once
   */
  async getMultipleQuotes(symbols: string[]): Promise<{ [symbol: string]: FinnhubQuote | null }> {
    const results: { [symbol: string]: FinnhubQuote | null } = {};
    
    // Process in batches to avoid rate limiting
    const batchSize = 5;
    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize);
      
      const promises = batch.map(async (symbol) => {
        const quote = await this.getQuote(symbol);
        return { symbol, quote };
      });

      const batchResults = await Promise.all(promises);
      
      batchResults.forEach(({ symbol, quote }) => {
        results[symbol] = quote;
      });

      // Add delay between batches to respect rate limits
      if (i + batchSize < symbols.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Format symbol for Finnhub API
   * Maps common symbols to Finnhub format
   */
  private formatSymbol(symbol: string): string {
    // Remove common prefixes/suffixes
    let formatted = symbol.toUpperCase();
    
    // Handle forex pairs
    if (formatted.includes('/')) {
      // Convert EUR/USD to OANDA:EUR_USD format
      const [base, quote] = formatted.split('/');
      return `OANDA:${base}_${quote}`;
    }

    // Handle crypto (should not be used with Finnhub, but just in case)
    if (formatted.includes('BTC') || formatted.includes('ETH') || formatted.includes('USDT')) {
      console.warn(`Cryptocurrency ${symbol} should not use Finnhub API`);
      return formatted;
    }

    // Handle commodities
    const commodityMap: { [key: string]: string } = {
      'GOLD': 'OANDA:XAU_USD',
      'SILVER': 'OANDA:XAG_USD',
      'OIL': 'OANDA:BCO_USD',
      'CRUDE': 'OANDA:BCO_USD',
    };

    if (commodityMap[formatted]) {
      return commodityMap[formatted];
    }

    // For regular stocks, return as-is
    return formatted;
  }

  /**
   * Check if symbol should use Finnhub (not crypto)
   */
  static shouldUseFinnhub(assetType: string): boolean {
    const cryptoTypes = ['CRYPTO', 'CRYPTOCURRENCY', 'DIGITAL_CURRENCY'];
    return !cryptoTypes.includes(assetType.toUpperCase());
  }

  /**
   * Convert Finnhub quote to our standard format
   */
  static convertToStandardFormat(quote: FinnhubQuote, symbol: string) {
    return {
      symbol,
      current_price: quote.c.toString(),
      price_change_24h: quote.d.toString(),
      price_change_percentage_24h: quote.dp.toString(),
      high_24h: quote.h.toString(),
      low_24h: quote.l.toString(),
      open_24h: quote.o.toString(),
      previous_close: quote.pc.toString(),
      last_updated: new Date(quote.t * 1000).toISOString(),
    };
  }
}

export const finnhubApi = new FinnhubApiService();
export default FinnhubApiService;
