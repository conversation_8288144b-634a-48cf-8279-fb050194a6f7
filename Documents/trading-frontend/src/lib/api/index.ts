// Export API client
export { apiClient, ApiError } from '../api-client';
export type { ApiResponse, User, LoginResponse } from '../api-client';

// Export trading API
export { TradingApiService } from './trading';
export type {
  Asset,
  AssetPrice,
  LiveQuote,
  SupportedAssets,
  Trade,
  TradingAccount,
} from './trading';

// Export professional trading API
export { ProfessionalTradingApiService } from './professional-trading';
export type {
  Instrument,
  Order,
  Position,
  Execution,
  MarketPrice,
  TradingSettings,
} from './professional-trading';

// Export Finnhub API
export { finnhubApi, FinnhubApiService } from './finnhub';
export type {
  FinnhubQuote,
  FinnhubCandle,
  FinnhubSymbolLookup,
} from './finnhub';

// Export wallet API
export { WalletApiService } from './wallet';
export type {
  Wallet,
  Transaction,
} from './wallet';

// Export user API
export { UserApiService } from './user';
export type {
  UserProfile,
} from './user';

// Re-export encryption utilities
export { SecureSessionStorage } from '../encryption';
