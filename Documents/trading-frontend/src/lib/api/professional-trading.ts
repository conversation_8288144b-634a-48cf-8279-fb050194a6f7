import { apiClient, ApiError } from '../api-client';
import type { ApiResponse, PaginatedResponse } from '../api-client';

// Professional Trading Types
export interface Instrument {
  id: string;
  symbol: string;
  name: string;
  instrument_type: 'STOCK' | 'FOREX' | 'CRYPTO' | 'COMMODITY' | 'INDEX';
  base_currency: string;
  quote_currency: string;
  min_quantity: string;
  max_quantity: string;
  tick_size: string;
  is_active: boolean;
}

export interface Order {
  id: string;
  account: string;
  instrument: {
    symbol: string;
    name: string;
  };
  order_type: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  side: 'BUY' | 'SELL';
  quantity: string;
  price?: string;
  status: 'PENDING' | 'FILLED' | 'PARTIALLY_FILLED' | 'CANCELLED' | 'REJECTED';
  filled_quantity: string;
  remaining_quantity: string;
  fill_percentage: number;
  time_in_force: 'GTC' | 'IOC' | 'FOK' | 'DAY';
  leverage?: number;
  created_at: string;
  updated_at: string;
}

export interface Position {
  id: string;
  instrument: {
    symbol: string;
    name: string;
  };
  side: 'LONG' | 'SHORT';
  entry_price: string;
  quantity: string;
  current_price: string;
  unrealized_pnl: string;
  total_pnl: string;
  pnl_percentage: number;
  status: 'OPEN' | 'CLOSED';
  opened_at: string;
  closed_at?: string;
}

export interface Execution {
  id: string;
  order: string;
  instrument: {
    symbol: string;
    name: string;
  };
  side: 'BUY' | 'SELL';
  price: string;
  quantity: string;
  fee: string;
  notional_value: string;
  net_amount: string;
  executed_at: string;
}

export interface MarketPrice {
  bid: string;
  ask: string;
  last_price: string;
  spread: string;
  timestamp: string;
}

export interface TradingSettings {
  id: string;
  asset: string;
  take_profit_threshold: string;
  stop_loss_threshold: string;
  max_leverage: number;
  min_leverage: number;
  min_trade_amount: string;
  max_trade_amount: string;
  trading_fee_percentage: string;
  spread_percentage: string;
  is_active: boolean;
}

/**
 * Professional Trading API Service
 */
export class ProfessionalTradingApiService {
  /**
   * Get all instruments
   */
  static async getInstruments(params?: {
    page?: number;
    page_size?: number;
    instrument_type?: string;
  }): Promise<ApiResponse<PaginatedResponse<Instrument>>> {
    return apiClient.get<PaginatedResponse<Instrument>>('/instruments/', params);
  }

  /**
   * Get instruments by type
   */
  static async getInstrumentsByType(type: string): Promise<ApiResponse<Instrument[]>> {
    return apiClient.get<Instrument[]>('/instruments/by_type/', { type });
  }

  /**
   * Place order
   */
  static async placeOrder(data: {
    account_id: string;
    instrument_id: string;
    order_type: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
    side: 'BUY' | 'SELL';
    quantity: string;
    price?: string;
    time_in_force?: 'GTC' | 'IOC' | 'FOK' | 'DAY';
    leverage?: number;
  }): Promise<ApiResponse<Order>> {
    return apiClient.post<Order>('/orders/', data);
  }

  /**
   * Get orders
   */
  static async getOrders(params?: {
    page?: number;
    page_size?: number;
    status?: string;
  }): Promise<ApiResponse<PaginatedResponse<Order>>> {
    return apiClient.get<PaginatedResponse<Order>>('/orders/', params);
  }

  /**
   * Get open orders
   */
  static async getOpenOrders(): Promise<ApiResponse<Order[]>> {
    return apiClient.get<Order[]>('/orders/open_orders/');
  }

  /**
   * Cancel order
   */
  static async cancelOrder(orderId: string, reason?: string): Promise<ApiResponse<Order>> {
    return apiClient.post<Order>(`/orders/${orderId}/cancel/`, { reason: reason || 'User cancelled' });
  }

  /**
   * Get positions
   */
  static async getPositions(params?: {
    page?: number;
    page_size?: number;
    status?: string;
  }): Promise<ApiResponse<PaginatedResponse<Position>>> {
    return apiClient.get<PaginatedResponse<Position>>('/positions/', params);
  }

  /**
   * Get open positions
   */
  static async getOpenPositions(): Promise<ApiResponse<Position[]>> {
    return apiClient.get<Position[]>('/positions/open_positions/');
  }

  /**
   * Close position
   */
  static async closePosition(positionId: string, data: {
    quantity?: string;
    reason?: string;
  }): Promise<ApiResponse<Position>> {
    return apiClient.post<Position>(`/positions/${positionId}/close/`, data);
  }

  /**
   * Get executions
   */
  static async getExecutions(params?: {
    page?: number;
    page_size?: number;
  }): Promise<ApiResponse<PaginatedResponse<Execution>>> {
    return apiClient.get<PaginatedResponse<Execution>>('/executions/', params);
  }

  /**
   * Get latest market prices
   */
  static async getLatestMarketPrices(): Promise<ApiResponse<{ [symbol: string]: MarketPrice }>> {
    return apiClient.get<{ [symbol: string]: MarketPrice }>('/market-data/latest_prices/');
  }

  /**
   * Get trading settings
   */
  static async getTradingSettings(params?: {
    page?: number;
    page_size?: number;
  }): Promise<ApiResponse<PaginatedResponse<TradingSettings>>> {
    return apiClient.get<PaginatedResponse<TradingSettings>>('/trading-settings/', params);
  }
}

export default ProfessionalTradingApiService;
