import { apiClient, ApiResponse, PaginatedResponse, ApiError } from '../api-client';

// Trading related types
export interface Asset {
  id: string;
  symbol: string;
  name: string;
  asset_type: 'STOCK' | 'FOREX' | 'CRYPTO' | 'COMMODITY' | 'INDEX';
  description: string;
  is_active: boolean;
  data_source: string;
  last_price_update: string;
}

export interface AssetPrice {
  id: string;
  asset: string;
  price: string;
  open_price: string;
  high_price: string;
  low_price: string;
  volume: number;
  change: string;
  change_percent: string;
  data_source: string;
  timestamp: string;
}

export interface LiveQuote {
  asset: {
    symbol: string;
    name: string;
    asset_type: string;
  };
  live_data: {
    price: string;
    open: string;
    high: string;
    low: string;
    volume: number;
    change: string;
    change_percent: string;
  };
  source: string;
  timestamp: string;
}

export interface SupportedAssets {
  current_assets: {
    STOCK: Asset[];
    FOREX: Asset[];
    CRYPTO: Asset[];
    COMMODITY: Asset[];
    INDEX: Asset[];
  };
  timestamp: string;
}

export interface Trade {
  id: string;
  account: string;
  asset: string;
  trade_type: 'BUY' | 'SELL';
  order_type: 'MARKET' | 'LIMIT';
  status: 'OPEN' | 'CLOSED' | 'PENDING' | 'CANCELLED';
  open_price: string;
  volume: string;
  leverage: number;
  take_profit?: string;
  stop_loss?: string;
  profit_loss: string;
  open_time: string;
  close_time?: string;
}

export interface TradingAccount {
  id: string;
  account_number: string;
  name: string;
  wallet: string;
  balance: string;
  leverage: number;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  created_at: string;
}

/**
 * Trading API Service
 */
export class TradingApiService {
  /**
   * Get all assets
   */
  static async getAssets(params?: {
    asset_type?: string;
    is_active?: boolean;
    page?: number;
    page_size?: number;
  }): Promise<ApiResponse<PaginatedResponse<Asset>>> {
    return apiClient.get<PaginatedResponse<Asset>>('/assets/', params);
  }

  /**
   * Get asset details
   */
  static async getAsset(assetId: string): Promise<ApiResponse<Asset>> {
    return apiClient.get<Asset>(`/assets/${assetId}/`);
  }

  /**
   * Get asset prices
   */
  static async getAssetPrices(params?: {
    asset_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<AssetPrice[]>> {
    return apiClient.get<AssetPrice[]>('/asset-prices/', params);
  }

  /**
   * Get live quote for a symbol
   */
  static async getLiveQuote(symbol: string): Promise<ApiResponse<LiveQuote>> {
    return apiClient.get<LiveQuote>('/alpha-vantage/live_quote/', { symbol });
  }

  /**
   * Get price history for a symbol
   */
  static async getPriceHistory(params: {
    symbol: string;
    days?: number;
  }): Promise<ApiResponse<AssetPrice[]>> {
    return apiClient.get<AssetPrice[]>('/alpha-vantage/price_history/', params);
  }

  /**
   * Get supported assets from Alpha Vantage
   */
  static async getSupportedAssets(): Promise<ApiResponse<SupportedAssets>> {
    return apiClient.get<SupportedAssets>('/alpha-vantage/supported_assets/');
  }

  /**
   * Get user's trading accounts
   */
  static async getTradingAccounts(): Promise<ApiResponse<PaginatedResponse<TradingAccount>>> {
    return apiClient.get<PaginatedResponse<TradingAccount>>('/trading-accounts/');
  }

  /**
   * Create trading account
   */
  static async createTradingAccount(data: {
    name: string;
    wallet_id: string;
    leverage?: number;
  }): Promise<ApiResponse<TradingAccount>> {
    // Use correct field naming convention with _id suffix
    const apiData = {
      name: data.name,
      wallet_id: data.wallet_id, // Django expects 'wallet_id' field
      leverage: data.leverage || 1,
    };

    try {
      return await apiClient.post<TradingAccount>('/trading-accounts/', apiData);
    } catch (error: any) {
      // Handle specific validation errors
      if (error.status === 400 && error.data) {
        if (error.data.wallet_id) {
          throw new ApiError(
            'Please select a valid wallet for this trading account.',
            error.status,
            error.data
          );
        }
        if (error.data.detail && error.data.detail.includes('maximum')) {
          throw new ApiError(
            `Account limit reached. ${error.data.detail}`,
            error.status,
            error.data
          );
        }
      }
      throw error;
    }
  }

  /**
   * Get trading account details
   */
  static async getTradingAccount(accountId: string): Promise<ApiResponse<TradingAccount>> {
    return apiClient.get<TradingAccount>(`/trading-accounts/${accountId}/`);
  }

  /**
   * Get user's trades
   */
  static async getTrades(params?: {
    status?: string;
    asset_id?: string;
  }): Promise<ApiResponse<PaginatedResponse<Trade>>> {
    return apiClient.get<PaginatedResponse<Trade>>('/trades/', params);
  }

  /**
   * Place a trade
   */
  static async placeTrade(data: {
    account_id: string;
    asset_id: string;
    trade_type: 'BUY' | 'SELL';
    order_type: 'MARKET' | 'LIMIT';
    volume: string;
    leverage?: number;
    take_profit?: string;
    stop_loss?: string;
  }): Promise<ApiResponse<Trade>> {
    // Use correct field naming convention with _id suffix
    const apiData = {
      account_id: data.account_id,
      asset_id: data.asset_id,
      trade_type: data.trade_type,
      order_type: data.order_type,
      volume: data.volume,
      leverage: data.leverage || 1,
      take_profit: data.take_profit,
      stop_loss: data.stop_loss,
    };

    return apiClient.post<Trade>('/trades/', apiData);
  }

  /**
   * Close a trade
   */
  static async closeTrade(tradeId: string, reason?: string): Promise<ApiResponse<Trade>> {
    return apiClient.post<Trade>(`/trades/${tradeId}/close/`, { reason: reason || 'MANUAL' });
  }

  /**
   * Get trade details
   */
  static async getTrade(tradeId: string): Promise<ApiResponse<Trade>> {
    return apiClient.get<Trade>(`/trades/${tradeId}/`);
  }

  /**
   * Manual price refresh (Admin only)
   */
  static async refreshPrices(): Promise<ApiResponse<{ message: string; task_id: string; timestamp: string }>> {
    return apiClient.post('/alpha-vantage/refresh_prices/');
  }

  /**
   * Update trading account
   */
  static async updateTradingAccount(accountId: string, data: {
    name?: string;
    leverage?: number;
  }): Promise<ApiResponse<TradingAccount>> {
    return apiClient.patch<TradingAccount>(`/trading-accounts/${accountId}/`, data);
  }

  /**
   * Transfer from trading account to wallet
   */
  static async transferToWallet(accountId: string, data: {
    amount: string;
    description?: string;
  }): Promise<ApiResponse<{
    message: string;
    account_balance: string;
    wallet_balance: string;
    amount_transferred: string;
  }>> {
    return apiClient.post(`/trading-accounts/${accountId}/transfer_to_wallet/`, data);
  }
}

export default TradingApiService;
