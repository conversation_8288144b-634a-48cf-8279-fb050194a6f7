import { apiClient, ApiResponse, User, PaginatedResponse } from '../api-client';

// Extended user profile type
export interface UserProfile extends User {
  phone_number?: string;
  address?: string;
  date_of_birth?: string;
  profile_picture?: string;
  created_at: string;
  updated_at: string;
}

/**
 * User API Service
 */
export class UserApiService {
  /**
   * Get user profile
   */
  static async getUserProfile(userId: string): Promise<ApiResponse<UserProfile>> {
    return apiClient.get<UserProfile>(`/users/${userId}/`);
  }

  /**
   * Update user profile
   */
  static async updateUserProfile(userId: string, data: {
    first_name?: string;
    last_name?: string;
    phone_number?: string;
    address?: string;
    date_of_birth?: string;
  }): Promise<ApiResponse<UserProfile>> {
    return apiClient.patch<UserProfile>(`/users/${userId}/`, data);
  }

  /**
   * Upload profile picture
   */
  static async uploadProfilePicture(userId: string, file: File): Promise<ApiResponse<UserProfile>> {
    const formData = new FormData();
    formData.append('profile_picture', file);

    return apiClient.upload<UserProfile>(`/users/${userId}/`, formData);
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.post<{ message: string }>('/auth/password-reset-request/', {
      email,
    });
  }

  /**
   * Confirm password reset
   */
  static async confirmPasswordReset(data: {
    token: string;
    new_password: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return apiClient.post<{ message: string }>('/auth/password-reset-confirm/', data);
  }
}

export default UserApiService;
