import { apiClient, ApiResponse, PaginatedResponse, ApiError } from '../api-client';

// Wallet related types
export interface Wallet {
  id: string;
  name: string;
  currency: string;
  balance: string;
  is_active: boolean;
  created_at: string;
}

export interface Transaction {
  id: string;
  wallet: string;
  transaction_type: 'DEPOSIT' | 'WITHDRAWAL';
  amount: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  payment_method: 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL';
  reference: string;
  description: string;
  created_at: string;
  updated_at: string;
}

/**
 * Wallet API Service
 */
export class WalletApiService {
  /**
   * Get user's wallets
   */
  static async getWallets(): Promise<ApiResponse<PaginatedResponse<Wallet>>> {
    return apiClient.get<PaginatedResponse<Wallet>>('/wallets/');
  }

  /**
   * Create a new wallet
   */
  static async createWallet(data: {
    name: string;
    currency: string;
  }): Promise<ApiResponse<Wallet>> {
    try {
      return await apiClient.post<Wallet>('/wallets/', data);
    } catch (error: any) {
      // Handle specific wallet limit errors
      if (error.status === 400 && error.data?.detail) {
        if (error.data.detail.includes('maximum wallet limit')) {
          throw new ApiError(
            `Wallet limit reached. ${error.data.detail}`,
            error.status,
            error.data
          );
        }
      }
      throw error;
    }
  }

  /**
   * Get wallet details
   */
  static async getWallet(walletId: string): Promise<ApiResponse<Wallet>> {
    return apiClient.get<Wallet>(`/wallets/${walletId}/`);
  }

  /**
   * Update wallet
   */
  static async updateWallet(walletId: string, data: {
    name?: string;
  }): Promise<ApiResponse<Wallet>> {
    return apiClient.patch<Wallet>(`/wallets/${walletId}/`, data);
  }

  /**
   * Get transactions
   */
  static async getTransactions(params?: {
    wallet_id?: string;
    transaction_type?: 'DEPOSIT' | 'WITHDRAWAL';
    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  }): Promise<ApiResponse<PaginatedResponse<Transaction>>> {
    return apiClient.get<PaginatedResponse<Transaction>>('/transactions/', params);
  }

  /**
   * Create deposit transaction
   */
  static async createDeposit(data: {
    wallet_id: string;
    amount: string;
    payment_method: 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL';
    description?: string;
  }): Promise<ApiResponse<Transaction>> {
    // Use correct field naming convention with _id suffix
    return apiClient.post<Transaction>('/transactions/', {
      wallet_id: data.wallet_id,
      amount: data.amount,
      payment_method: data.payment_method,
      description: data.description,
      transaction_type: 'DEPOSIT',
    });
  }

  /**
   * Create withdrawal transaction
   */
  static async createWithdrawal(data: {
    wallet_id: string;
    amount: string;
    payment_method: 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL';
    description?: string;
  }): Promise<ApiResponse<Transaction>> {
    // Use correct field naming convention with _id suffix
    return apiClient.post<Transaction>('/transactions/', {
      wallet_id: data.wallet_id,
      amount: data.amount,
      payment_method: data.payment_method,
      description: data.description,
      transaction_type: 'WITHDRAWAL',
    });
  }

  /**
   * Get transaction details
   */
  static async getTransaction(transactionId: string): Promise<ApiResponse<Transaction>> {
    return apiClient.get<Transaction>(`/transactions/${transactionId}/`);
  }

  /**
   * Get wallet details
   */
  static async getWallet(walletId: string): Promise<ApiResponse<Wallet>> {
    return apiClient.get<Wallet>(`/wallets/${walletId}/`);
  }

  /**
   * Update wallet
   */
  static async updateWallet(walletId: string, data: {
    name?: string;
  }): Promise<ApiResponse<Wallet>> {
    return apiClient.patch<Wallet>(`/wallets/${walletId}/`, data);
  }

  /**
   * Transfer from wallet to trading account
   */
  static async transferToAccount(walletId: string, data: {
    trading_account_id: string;
    amount: string;
    description?: string;
  }): Promise<ApiResponse<{
    message: string;
    wallet_balance: string;
    account_balance: string;
    amount_transferred: string;
  }>> {
    return apiClient.post(`/wallets/${walletId}/transfer_to_account/`, data);
  }
}

export default WalletApiService;
