import CryptoJS from 'crypto-js';

// Secret key for encryption - in production, this should be from environment variables
const SECRET_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'trading-platform-secret-key-2024';

/**
 * Encrypt data before storing in session storage
 */
export const encryptData = (data: any): string => {
  try {
    const jsonString = JSON.stringify(data);
    const encrypted = CryptoJS.AES.encrypt(jsonString, SECRET_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypt data retrieved from session storage
 */
export const decryptData = (encryptedData: string): any => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedString);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};

/**
 * Secure session storage wrapper with encryption
 */
export class SecureSessionStorage {
  /**
   * Store encrypted data in session storage
   */
  static setItem(key: string, value: any): void {
    try {
      const encryptedValue = encryptData(value);
      sessionStorage.setItem(key, encryptedValue);
    } catch (error) {
      console.error('Failed to store encrypted data:', error);
    }
  }

  /**
   * Retrieve and decrypt data from session storage
   */
  static getItem(key: string): any {
    try {
      const encryptedValue = sessionStorage.getItem(key);
      if (!encryptedValue) return null;
      return decryptData(encryptedValue);
    } catch (error) {
      console.error('Failed to retrieve encrypted data:', error);
      // Remove corrupted data
      sessionStorage.removeItem(key);
      return null;
    }
  }

  /**
   * Remove item from session storage
   */
  static removeItem(key: string): void {
    sessionStorage.removeItem(key);
  }

  /**
   * Clear all session storage
   */
  static clear(): void {
    sessionStorage.clear();
  }

  /**
   * Check if key exists in session storage
   */
  static hasItem(key: string): boolean {
    return sessionStorage.getItem(key) !== null;
  }
}
