// Mock data service for trading interface
// This will be replaced with real API calls later

import { Asset } from '../api/trading';

// Asset types
export const ASSET_TYPES = {
  CRYPTO: 'CRYPTO',
  FOREX: 'FOREX',
  STOCK: 'STOCK',
  INDEX: 'INDEX',
  COMMODITY: 'COMMODITY',
};

// Mock assets data
export const mockAssets: Asset[] = [
  // Crypto assets
  {
    id: 'btc-usd',
    symbol: 'BTC/USD',
    name: 'Bitcoin',
    asset_type: ASSET_TYPES.CRYPTO,
    current_price: '36789.45',
    price_change_24h: '1245.67',
    price_change_percentage_24h: '3.5',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'eth-usd',
    symbol: 'ETH/USD',
    name: 'Ethereum',
    asset_type: ASSET_TYPES.CRYPTO,
    current_price: '2456.78',
    price_change_24h: '-123.45',
    price_change_percentage_24h: '-4.8',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'sol-usd',
    symbol: 'SOL/USD',
    name: '<PERSON><PERSON>',
    asset_type: ASSET_TYPES.CRYPTO,
    current_price: '98.76',
    price_change_24h: '5.43',
    price_change_percentage_24h: '5.8',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'ada-usd',
    symbol: 'ADA/USD',
    name: 'Cardano',
    asset_type: ASSET_TYPES.CRYPTO,
    current_price: '0.45',
    price_change_24h: '0.02',
    price_change_percentage_24h: '4.6',
    last_updated: new Date().toISOString(),
  },
  
  // Forex assets
  {
    id: 'eur-usd',
    symbol: 'EUR/USD',
    name: 'Euro / US Dollar',
    asset_type: ASSET_TYPES.FOREX,
    current_price: '1.0876',
    price_change_24h: '-0.0023',
    price_change_percentage_24h: '-0.21',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'gbp-usd',
    symbol: 'GBP/USD',
    name: 'British Pound / US Dollar',
    asset_type: ASSET_TYPES.FOREX,
    current_price: '1.2654',
    price_change_24h: '0.0045',
    price_change_percentage_24h: '0.36',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'usd-jpy',
    symbol: 'USD/JPY',
    name: 'US Dollar / Japanese Yen',
    asset_type: ASSET_TYPES.FOREX,
    current_price: '149.87',
    price_change_24h: '0.76',
    price_change_percentage_24h: '0.51',
    last_updated: new Date().toISOString(),
  },
  
  // Stock assets
  {
    id: 'aapl',
    symbol: 'AAPL',
    name: 'Apple Inc.',
    asset_type: ASSET_TYPES.STOCK,
    current_price: '187.45',
    price_change_24h: '3.21',
    price_change_percentage_24h: '1.74',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'msft',
    symbol: 'MSFT',
    name: 'Microsoft Corporation',
    asset_type: ASSET_TYPES.STOCK,
    current_price: '378.92',
    price_change_24h: '-2.34',
    price_change_percentage_24h: '-0.61',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'amzn',
    symbol: 'AMZN',
    name: 'Amazon.com Inc.',
    asset_type: ASSET_TYPES.STOCK,
    current_price: '178.12',
    price_change_24h: '1.87',
    price_change_percentage_24h: '1.06',
    last_updated: new Date().toISOString(),
  },
  
  // Index assets
  {
    id: 'spx',
    symbol: 'SPX',
    name: 'S&P 500',
    asset_type: ASSET_TYPES.INDEX,
    current_price: '5021.84',
    price_change_24h: '15.67',
    price_change_percentage_24h: '0.31',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'ndx',
    symbol: 'NDX',
    name: 'Nasdaq 100',
    asset_type: ASSET_TYPES.INDEX,
    current_price: '17654.32',
    price_change_24h: '87.65',
    price_change_percentage_24h: '0.50',
    last_updated: new Date().toISOString(),
  },
  
  // Commodity assets
  {
    id: 'xau-usd',
    symbol: 'XAU/USD',
    name: 'Gold',
    asset_type: ASSET_TYPES.COMMODITY,
    current_price: '2345.67',
    price_change_24h: '12.34',
    price_change_percentage_24h: '0.53',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'xag-usd',
    symbol: 'XAG/USD',
    name: 'Silver',
    asset_type: ASSET_TYPES.COMMODITY,
    current_price: '27.89',
    price_change_24h: '0.45',
    price_change_percentage_24h: '1.64',
    last_updated: new Date().toISOString(),
  },
  {
    id: 'wti',
    symbol: 'WTI',
    name: 'WTI Crude Oil',
    asset_type: ASSET_TYPES.COMMODITY,
    current_price: '76.54',
    price_change_24h: '-1.23',
    price_change_percentage_24h: '-1.58',
    last_updated: new Date().toISOString(),
  },
];

// Generate mock price history data
export const generateMockPriceHistory = (
  basePrice: number,
  volatility: number = 0.02,
  dataPoints: number = 100
) => {
  const prices = [];
  let currentPrice = basePrice;
  
  const now = new Date();
  const millisecondsPerDay = 24 * 60 * 60 * 1000;
  
  for (let i = dataPoints; i >= 0; i--) {
    // Random price movement based on volatility
    const change = currentPrice * (Math.random() * volatility * 2 - volatility);
    currentPrice += change;
    
    // Ensure price doesn't go negative
    if (currentPrice <= 0) {
      currentPrice = basePrice * 0.1;
    }
    
    const timestamp = new Date(now.getTime() - i * millisecondsPerDay / dataPoints);
    
    prices.push({
      timestamp: timestamp.toISOString(),
      price: currentPrice.toFixed(2),
    });
  }
  
  return prices;
};

// Get assets by type
export const getAssetsByType = (type: string) => {
  return mockAssets.filter(asset => asset.asset_type === type);
};

// Get asset by ID
export const getAssetById = (id: string) => {
  return mockAssets.find(asset => asset.id === id);
};

// Get price history for an asset
export const getAssetPriceHistory = (assetId: string) => {
  const asset = getAssetById(assetId);
  if (!asset) return [];
  
  return generateMockPriceHistory(parseFloat(asset.current_price));
};

// Mock function to simulate placing a trade
export const placeMockTrade = (
  assetId: string,
  accountId: string,
  tradeType: 'BUY' | 'SELL',
  amount: number,
  leverage: number
) => {
  const asset = getAssetById(assetId);
  if (!asset) throw new Error('Asset not found');
  
  return {
    id: `trade-${Date.now()}`,
    asset_id: assetId,
    account_id: accountId,
    trade_type: tradeType,
    amount: amount.toString(),
    leverage: leverage,
    open_price: asset.current_price,
    status: 'OPEN',
    created_at: new Date().toISOString(),
  };
};
