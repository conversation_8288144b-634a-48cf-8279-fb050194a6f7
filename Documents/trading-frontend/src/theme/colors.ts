/**
 * Theme color configuration for the Trading Platform
 * 
 * This file contains color definitions for both light and dark themes.
 * Colors are organized by semantic purpose rather than specific color names
 * to make theme customization easier.
 */

export type ColorMode = 'light' | 'dark';

export interface ThemeColors {
  // Background colors
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    accent: string;
  };
  
  // Text colors
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    accent: string;
    inverted: string;
  };
  
  // Border colors
  border: {
    primary: string;
    secondary: string;
    accent: string;
  };
  
  // Button colors
  button: {
    primary: {
      background: string;
      hover: string;
      text: string;
    };
    secondary: {
      background: string;
      hover: string;
      text: string;
    };
    danger: {
      background: string;
      hover: string;
      text: string;
    };
  };
  
  // Status colors
  status: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  
  // Chart colors
  chart: {
    positive: string;
    negative: string;
    neutral: string;
    grid: string;
    axis: string;
  };
}

export const lightTheme: ThemeColors = {
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6',
    accent: '#eff6ff',
  },
  text: {
    primary: '#111827',
    secondary: '#4b5563',
    tertiary: '#6b7280',
    accent: '#3b82f6',
    inverted: '#ffffff',
  },
  border: {
    primary: '#e5e7eb',
    secondary: '#d1d5db',
    accent: '#93c5fd',
  },
  button: {
    primary: {
      background: '#3b82f6',
      hover: '#2563eb',
      text: '#ffffff',
    },
    secondary: {
      background: '#f3f4f6',
      hover: '#e5e7eb',
      text: '#374151',
    },
    danger: {
      background: '#ef4444',
      hover: '#dc2626',
      text: '#ffffff',
    },
  },
  status: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  chart: {
    positive: '#10b981',
    negative: '#ef4444',
    neutral: '#6b7280',
    grid: '#e5e7eb',
    axis: '#9ca3af',
  },
};

export const darkTheme: ThemeColors = {
  background: {
    primary: '#111827',
    secondary: '#1f2937',
    tertiary: '#374151',
    accent: '#1e3a8a',
  },
  text: {
    primary: '#f9fafb',
    secondary: '#e5e7eb',
    tertiary: '#d1d5db',
    accent: '#60a5fa',
    inverted: '#111827',
  },
  border: {
    primary: '#374151',
    secondary: '#4b5563',
    accent: '#1e40af',
  },
  button: {
    primary: {
      background: '#3b82f6',
      hover: '#2563eb',
      text: '#ffffff',
    },
    secondary: {
      background: '#374151',
      hover: '#4b5563',
      text: '#f9fafb',
    },
    danger: {
      background: '#ef4444',
      hover: '#dc2626',
      text: '#ffffff',
    },
  },
  status: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  chart: {
    positive: '#10b981',
    negative: '#ef4444',
    neutral: '#9ca3af',
    grid: '#4b5563',
    axis: '#6b7280',
  },
};

export const getThemeColors = (mode: ColorMode): ThemeColors => {
  return mode === 'light' ? lightTheme : darkTheme;
};

export default {
  light: lightTheme,
  dark: darkTheme,
  getThemeColors,
};
