// Core API Types and Interfaces
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'USER' | 'ADMIN' | 'TRADER';
  account_type: 'BASIC' | 'PREMIUM' | 'VIP';
  is_verified: boolean;
  phone_number?: string;
  address?: string;
  date_of_birth?: string;
  profile_picture?: string;
  created_at: string;
  updated_at: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

// Wallet Types
export interface Wallet {
  id: string;
  user: string;
  name: string;
  currency: string;
  balance: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateWalletRequest {
  name: string;
  currency: string;
}

export interface UpdateWalletRequest {
  name?: string;
}

// Trading Account Types
export interface TradingAccount {
  id: string;
  user: string;
  wallet: string;
  account_number: string;
  name: string;
  currency: string;
  balance: string;
  leverage: number;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  created_at: string;
  updated_at: string;
}

export interface CreateTradingAccountRequest {
  name: string;
  wallet: string;
  leverage: number;
}

// Transaction Types
export interface Transaction {
  id: string;
  wallet: string;
  transaction_type: 'DEPOSIT' | 'WITHDRAWAL';
  amount: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  payment_method: 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL';
  reference: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface CreateTransactionRequest {
  wallet: string;
  transaction_type: 'DEPOSIT' | 'WITHDRAWAL';
  amount: string;
  payment_method: 'CARD' | 'BANK_TRANSFER' | 'CRYPTO' | 'PAYPAL';
  description?: string;
}

// Asset Types
export interface Asset {
  id: string;
  symbol: string;
  name: string;
  asset_type: 'STOCK' | 'FOREX' | 'CRYPTO' | 'COMMODITY' | 'INDEX';
  description: string;
  is_active: boolean;
  data_source: 'ALPHA_VANTAGE' | 'MANUAL';
  last_price_update: string;
  created_at: string;
  updated_at: string;
}

export interface AssetPrice {
  id: string;
  asset: string;
  price: string;
  open_price: string;
  high_price: string;
  low_price: string;
  volume: number;
  change: string;
  change_percent: string;
  data_source: 'ALPHA_VANTAGE' | 'MANUAL';
  timestamp: string;
}

// Alpha Vantage Types
export interface AlphaVantageAsset {
  symbol: string;
  name: string;
  asset_type: string;
}

export interface LiveQuoteResponse {
  asset: {
    symbol: string;
    name: string;
    asset_type: string;
  };
  live_data: {
    price: string;
    open: string;
    high: string;
    low: string;
    volume: number;
    change: string;
    change_percent: string;
  };
  source: string;
  timestamp: string;
}

export interface SupportedAssetsResponse {
  current_assets: {
    [key: string]: AlphaVantageAsset[];
  };
  timestamp: string;
}

// Trading Types
export interface Trade {
  id: string;
  user: string;
  account: string;
  asset: string;
  trade_type: 'BUY' | 'SELL';
  order_type: 'MARKET' | 'LIMIT' | 'STOP';
  status: 'OPEN' | 'CLOSED' | 'CANCELLED' | 'PENDING';
  open_price: string;
  close_price?: string;
  volume: string;
  leverage: number;
  take_profit?: string;
  stop_loss?: string;
  profit_loss: string;
  margin: string;
  open_time: string;
  close_time?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateTradeRequest {
  account_id: string;
  asset_id: string;
  trade_type: 'BUY' | 'SELL';
  order_type: 'MARKET' | 'LIMIT' | 'STOP';
  volume: string;
  leverage: number;
  take_profit?: string;
  stop_loss?: string;
  limit_price?: string; // For LIMIT orders
}

export interface CloseTradeRequest {
  reason: 'MANUAL' | 'TAKE_PROFIT' | 'STOP_LOSS' | 'MARGIN_CALL';
}

// Trading Settings Types
export interface TradingSettings {
  id: string;
  asset: string;
  take_profit_threshold: string;
  stop_loss_threshold: string;
  max_leverage: number;
  min_leverage: number;
  min_trade_amount: string;
  max_trade_amount: string;
  trading_fee_percentage: string;
  spread_percentage: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Query Parameters
export interface AssetQueryParams {
  asset_type?: string;
  is_active?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
}

export interface AssetPriceQueryParams {
  asset_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  page_size?: number;
}

export interface TransactionQueryParams {
  wallet_id?: string;
  transaction_type?: 'DEPOSIT' | 'WITHDRAWAL';
  status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  page?: number;
  page_size?: number;
}

export interface TradeQueryParams {
  account_id?: string;
  asset_id?: string;
  status?: 'OPEN' | 'CLOSED' | 'CANCELLED' | 'PENDING';
  trade_type?: 'BUY' | 'SELL';
  page?: number;
  page_size?: number;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  field?: string;
  details?: Record<string, any>;
}

export interface ValidationError {
  [field: string]: string[];
}

// State Types for Zustand stores
export interface LoadingState {
  [key: string]: boolean;
}

export interface ErrorState {
  [key: string]: string | null;
}
